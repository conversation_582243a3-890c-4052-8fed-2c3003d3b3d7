#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Report Templates for Electronic Raffle System
قوالب التقارير لنظام السحب الإلكتروني
"""

from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.lib.colors import Color, black, blue, red, green
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from datetime import datetime
import os

class ReportTemplate:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom styles for Arabic and English text"""
        # Arabic title style
        self.arabic_title = ParagraphStyle(
            'ArabicTitle',
            parent=self.styles['Title'],
            fontSize=20,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=blue
        )
        
        # English title style
        self.english_title = ParagraphStyle(
            'EnglishTitle',
            parent=self.styles['Title'],
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=15,
            textColor=blue
        )
        
        # Arabic heading style
        self.arabic_heading = ParagraphStyle(
            'ArabicHeading',
            parent=self.styles['Heading1'],
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=black
        )
        
        # English heading style
        self.english_heading = ParagraphStyle(
            'EnglishHeading',
            parent=self.styles['Heading1'],
            fontSize=12,
            alignment=TA_LEFT,
            spaceAfter=8,
            textColor=black
        )

class BasicReportTemplate(ReportTemplate):
    def generate_report(self, data, filename):
        """Generate basic raffle report"""
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []
        
        # Title
        title_ar = Paragraph("تقرير نتائج السحب الإلكتروني", self.arabic_title)
        title_en = Paragraph("Electronic Raffle Results Report", self.english_title)
        story.extend([title_ar, title_en, Spacer(1, 20)])
        
        # Date and basic info
        info_data = [
            ['التاريخ - Date:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['عدد المشاركين - Participants:', str(len(data.get('participants', [])))],
            ['عدد الفائزين - Winners:', str(len(data.get('winners', [])))]
        ]
        
        info_table = Table(info_data, colWidths=[3*inch, 2*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), Color(0.9, 0.9, 0.9)),
            ('TEXTCOLOR', (0, 0), (-1, -1), black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 0), (0, -1), Color(0.8, 0.8, 0.8)),
        ]))
        
        story.extend([info_table, Spacer(1, 20)])
        
        # Winners section
        winners_title = Paragraph("الفائزون - Winners", self.arabic_heading)
        story.append(winners_title)
        
        if data.get('winners'):
            winners_data = [['الترتيب - Rank', 'الاسم - Name']]
            for i, winner in enumerate(data['winners'], 1):
                winners_data.append([str(i), winner])
            
            winners_table = Table(winners_data, colWidths=[1*inch, 4*inch])
            winners_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), Color(0.2, 0.4, 0.8)),
                ('TEXTCOLOR', (0, 0), (-1, 0), Color(1, 1, 1)),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('BACKGROUND', (0, 1), (-1, -1), Color(0.95, 0.95, 0.95)),
                ('GRID', (0, 0), (-1, -1), 1, black)
            ]))
            
            story.append(winners_table)
        
        # Build PDF
        doc.build(story)

class DetailedReportTemplate(ReportTemplate):
    def generate_report(self, data, filename):
        """Generate detailed raffle report with statistics"""
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []
        
        # Enhanced title with logo space
        title_ar = Paragraph("تقرير مفصل - نتائج السحب الإلكتروني", self.arabic_title)
        title_en = Paragraph("Detailed Electronic Raffle Results Report", self.english_title)
        story.extend([title_ar, title_en, Spacer(1, 30)])
        
        # Executive summary
        summary_title = Paragraph("الملخص التنفيذي - Executive Summary", self.arabic_heading)
        story.append(summary_title)
        
        summary_data = [
            ['البيان - Item', 'القيمة - Value'],
            ['تاريخ السحب - Raffle Date', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['إجمالي المشاركين - Total Participants', str(len(data.get('participants', [])))],
            ['عدد الفائزين - Number of Winners', str(len(data.get('winners', [])))],
            ['فئات الجوائز - Prize Categories', str(len(data.get('prize_categories', [])))],
            ['نسبة الفوز - Win Percentage', f"{(len(data.get('winners', [])) / max(len(data.get('participants', [])), 1) * 100):.1f}%"]
        ]
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), Color(0.2, 0.4, 0.8)),
            ('TEXTCOLOR', (0, 0), (-1, 0), Color(1, 1, 1)),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 1), (-1, -1), Color(0.95, 0.95, 0.95)),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        story.extend([summary_table, Spacer(1, 20)])
        
        # Prize categories section
        if data.get('prize_categories'):
            categories_title = Paragraph("فئات الجوائز - Prize Categories", self.arabic_heading)
            story.append(categories_title)
            
            for i, category in enumerate(data['prize_categories'], 1):
                cat_data = [
                    [f"الفئة {i} - Category {i}", category.get('name', 'غير محدد')],
                    ['الوصف - Description', category.get('description', 'لا يوجد وصف')],
                    ['عدد الفائزين - Winners Count', str(category.get('winners_count', 0))]
                ]
                
                cat_table = Table(cat_data, colWidths=[2*inch, 3*inch])
                cat_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (0, -1), Color(0.8, 0.8, 0.8)),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, black)
                ]))
                
                story.extend([cat_table, Spacer(1, 10)])
        
        # Winners detailed section
        winners_title = Paragraph("تفاصيل الفائزين - Winners Details", self.arabic_heading)
        story.append(winners_title)
        
        if data.get('winners'):
            winners_data = [['#', 'الاسم - Name', 'وقت الفوز - Win Time']]
            for i, winner in enumerate(data['winners'], 1):
                win_time = datetime.now().strftime('%H:%M:%S')
                winners_data.append([str(i), winner, win_time])
            
            winners_table = Table(winners_data, colWidths=[0.5*inch, 3*inch, 1.5*inch])
            winners_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), Color(0.2, 0.6, 0.2)),
                ('TEXTCOLOR', (0, 0), (-1, 0), Color(1, 1, 1)),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('BACKGROUND', (0, 1), (-1, -1), Color(0.95, 0.95, 0.95)),
                ('GRID', (0, 0), (-1, -1), 1, black)
            ]))
            
            story.append(winners_table)
        
        # Footer
        story.append(Spacer(1, 30))
        footer = Paragraph(
            "تم إنشاء هذا التقرير بواسطة نظام السحب الإلكتروني<br/>Generated by Electronic Raffle System",
            ParagraphStyle('Footer', fontSize=8, alignment=TA_CENTER, textColor=Color(0.5, 0.5, 0.5))
        )
        story.append(footer)
        
        # Build PDF
        doc.build(story)

class CertificateTemplate(ReportTemplate):
    def generate_certificate(self, winner_name, prize_description, filename):
        """Generate winner certificate"""
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        # Border
        c.setStrokeColor(blue)
        c.setLineWidth(3)
        c.rect(50, 50, width-100, height-100)
        
        # Inner border
        c.setStrokeColor(Color(0.8, 0.8, 0.8))
        c.setLineWidth(1)
        c.rect(70, 70, width-140, height-140)
        
        # Title
        c.setFont("Helvetica-Bold", 24)
        c.setFillColor(blue)
        c.drawCentredString(width/2, height-150, "شهادة فوز - Winner Certificate")

        # Winner name
        c.setFont("Helvetica-Bold", 20)
        c.setFillColor(red)
        c.drawCentredString(width/2, height-250, f"المكرم / المكرمة: {winner_name}")
        c.drawCentredString(width/2, height-280, f"Dear: {winner_name}")

        # Congratulations message
        c.setFont("Helvetica", 16)
        c.setFillColor(black)
        c.drawCentredString(width/2, height-350, "تهانينا لفوزكم في السحب")
        c.drawCentredString(width/2, height-375, "Congratulations on winning the raffle")

        # Prize description
        if prize_description:
            c.setFont("Helvetica-Bold", 14)
            c.setFillColor(green)
            c.drawCentredString(width/2, height-420, f"الجائزة: {prize_description}")
            c.drawCentredString(width/2, height-440, f"Prize: {prize_description}")

        # Date
        c.setFont("Helvetica", 12)
        c.setFillColor(black)
        c.drawCentredString(width/2, height-520, f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}")
        c.drawCentredString(width/2, height-540, f"Date: {datetime.now().strftime('%Y-%m-%d')}")

        # Signature line
        c.setFont("Helvetica", 10)
        c.drawCentredString(width/2, height-620, "التوقيع - Signature")
        c.line(width/2-100, height-640, width/2+100, height-640)
        
        c.save()

def create_report_templates():
    """Create sample report templates"""
    print("📄 إنشاء قوالب التقارير...")
    print("📄 Creating report templates...")
    
    # Create templates directory
    if not os.path.exists('templates'):
        os.makedirs('templates')
        print("📁 Created templates directory")
    
    # Sample data for testing
    sample_data = {
        'participants': ['أحمد محمد', 'فاطمة علي', 'محمد سعد', 'نورا أحمد', 'خالد فهد'],
        'winners': ['أحمد محمد', 'نورا أحمد'],
        'prize_categories': [
            {
                'name': 'الجائزة الأولى',
                'description': 'جهاز كمبيوتر محمول',
                'winners_count': 1
            },
            {
                'name': 'الجائزة الثانية', 
                'description': 'قسيمة شراء بقيمة 500 ريال',
                'winners_count': 1
            }
        ]
    }
    
    # Generate sample reports
    basic_template = BasicReportTemplate()
    basic_template.generate_report(sample_data, 'templates/basic_report_sample.pdf')
    print("✅ Basic report template created")
    
    detailed_template = DetailedReportTemplate()
    detailed_template.generate_report(sample_data, 'templates/detailed_report_sample.pdf')
    print("✅ Detailed report template created")
    
    certificate_template = CertificateTemplate()
    certificate_template.generate_certificate(
        'أحمد محمد علي',
        'جهاز كمبيوتر محمول',
        'templates/winner_certificate_sample.pdf'
    )
    print("✅ Certificate template created")
    
    print("\n📁 قوالب التقارير المنشأة:")
    print("📁 Created report templates:")
    print("   📄 basic_report_sample.pdf - تقرير أساسي")
    print("   📄 detailed_report_sample.pdf - تقرير مفصل")
    print("   📄 winner_certificate_sample.pdf - شهادة فوز")

if __name__ == "__main__":
    create_report_templates()
