# دليل المستخدم - User Manual
## نظام السحب الإلكتروني - Electronic Raffle System

---

## 📋 جدول المحتويات - Table of Contents

1. [مقدمة - Introduction](#introduction)
2. [التثبيت والإعداد - Installation & Setup](#installation)
3. [واجهة المستخدم - User Interface](#interface)
4. [دليل الاستخدام خطوة بخطوة - Step-by-Step Guide](#guide)
5. [الميزات المتقدمة - Advanced Features](#advanced)
6. [استكشاف الأخطاء - Troubleshooting](#troubleshooting)
7. [الأسئلة الشائعة - FAQ](#faq)

---

## 🌟 مقدمة - Introduction {#introduction}

نظام السحب الإلكتروني هو تطبيق متطور مصمم لإجراء عمليات السحب العشوائي بطريقة احترافية ومثيرة. يدعم النظام اللغتين العربية والإنجليزية ويوفر واجهة مستخدم جذابة مع رسوم متحركة مذهلة.

### المميزات الأساسية:
- ✅ واجهة ثنائية اللغة (عربي/إنجليزي)
- ✅ استيراد المشاركين من Excel/CSV
- ✅ رسوم متحركة أثناء السحب
- ✅ تقارير PDF احترافية
- ✅ تصدير النتائج إلى Excel
- ✅ إعدادات قابلة للتخصيص

---

## 🔧 التثبيت والإعداد - Installation & Setup {#installation}

### المتطلبات الأساسية:
- **Python 3.7** أو أحدث
- **نظام التشغيل**: Windows, macOS, Linux
- **ذاكرة**: 512 MB RAM كحد أدنى
- **مساحة القرص**: 100 MB مساحة فارغة

### خطوات التثبيت:

#### الطريقة الأولى: التثبيت التلقائي
```bash
# تشغيل سكريبت الإعداد
python setup.py
```

#### الطريقة الثانية: التثبيت اليدوي
```bash
# تثبيت المكتبات المطلوبة
pip install pandas openpyxl reportlab

# إنشاء البيانات التجريبية
python create_sample_data.py

# تشغيل البرنامج
python electronic_raffle_system.py
```

#### للمستخدمين على Windows:
```bash
# تشغيل الملف المجمع
run_raffle_system.bat
```

---

## 🖥️ واجهة المستخدم - User Interface {#interface}

### اللوحة اليسرى - Control Panel:

#### 1. قسم المشاركين - Participants Section
- **استيراد من Excel**: لاستيراد قائمة المشاركين من ملف Excel أو CSV
- **إضافة يدوية**: لإضافة المشاركين واحداً تلو الآخر
- **عداد المشاركين**: يعرض العدد الإجمالي للمشاركين

#### 2. إعدادات السحب - Raffle Settings
- **عدد الفائزين**: تحديد كم فائز تريد اختيارهم (1-100)
- **السماح بالفوز المتكرر**: السماح للشخص بالفوز أكثر من مرة
- **سرعة الحركة**: تحديد سرعة الرسوم المتحركة (50-500 مللي ثانية)

#### 3. الإجراءات - Actions
- **🎲 بدء السحب**: بدء عملية السحب مع الرسوم المتحركة
- **📊 عرض النتائج**: عرض نافذة مفصلة بالنتائج
- **📄 طباعة PDF**: إنشاء تقرير PDF احترافي
- **🔄 إعادة تعيين**: مسح جميع البيانات والبدء من جديد

### اللوحة اليمنى - Display Panel:
- **شاشة العرض الرئيسية**: تعرض الرسوم المتحركة والنتائج
- **منطقة عرض الفائزين**: تعرض قائمة الفائزين أثناء وبعد السحب

---

## 📖 دليل الاستخدام خطوة بخطوة - Step-by-Step Guide {#guide}

### الخطوة 1: إعداد قائمة المشاركين

#### استيراد من Excel:
1. اضغط على **"استيراد من Excel"**
2. اختر ملف Excel أو CSV يحتوي على أسماء المشاركين
3. تأكد أن الأسماء في العمود الأول
4. اضغط **"فتح"** لاستيراد القائمة

#### الإضافة اليدوية:
1. اضغط على **"إضافة يدوية"**
2. أدخل اسم المشارك في الحقل
3. اضغط **"إضافة"** أو Enter
4. كرر العملية لجميع المشاركين

### الخطوة 2: تخصيص إعدادات السحب

1. **حدد عدد الفائزين**: استخدم المربع الدوار لتحديد العدد المطلوب
2. **اختر إعدادات الفوز المتكرر**: 
   - ✅ مفعل: يمكن للشخص الفوز أكثر من مرة
   - ❌ معطل: كل شخص يفوز مرة واحدة فقط
3. **اضبط سرعة الحركة**: استخدم الشريط المنزلق لتحديد السرعة

### الخطوة 3: إجراء السحب

1. تأكد من وجود مشاركين في القائمة
2. اضغط على **"🎲 بدء السحب"**
3. استمتع بمشاهدة الرسوم المتحركة
4. انتظر حتى يتم الإعلان عن جميع الفائزين
5. ستظهر رسالة تأكيد عند انتهاء السحب

### الخطوة 4: عرض وحفظ النتائج

#### عرض النتائج:
1. اضغط على **"📊 عرض النتائج"**
2. ستفتح نافذة جديدة تحتوي على:
   - تاريخ ووقت السحب
   - عدد المشاركين والفائزين
   - قائمة مفصلة بالفائزين
   - قائمة جميع المشاركين

#### حفظ تقرير PDF:
1. اضغط على **"📄 طباعة PDF"**
2. اختر مكان الحفظ واسم الملف
3. اضغط **"حفظ"**
4. سيتم إنشاء تقرير احترافي بصيغة PDF

#### تصدير إلى Excel:
1. من نافذة النتائج، اضغط **"حفظ كـ Excel"**
2. اختر مكان الحفظ
3. سيتم إنشاء ملف Excel بورقتين:
   - ورقة الفائزين
   - ورقة جميع المشاركين

---

## 🚀 الميزات المتقدمة - Advanced Features {#advanced}

### 1. تخصيص الواجهة
- **تغيير الألوان**: يمكن تعديل ألوان الواجهة في الكود
- **إضافة الشعارات**: إمكانية إضافة شعار المؤسسة
- **تخصيص النصوص**: تعديل الرسائل والعناوين

### 2. إعدادات متقدمة للسحب
- **فئات الجوائز**: إمكانية تقسيم الجوائز لفئات مختلفة
- **أوزان المشاركين**: إعطاء فرص أكبر لمشاركين معينين
- **استبعاد مشاركين**: منع مشاركين معينين من السحب

### 3. التقارير المتقدمة
- **إحصائيات مفصلة**: تحليل بيانات المشاركة
- **تقارير متعددة الصفحات**: للفعاليات الكبيرة
- **تصدير بصيغ متعددة**: PDF, Excel, CSV, JSON

### 4. الأتمتة والبرمجة
- **واجهة برمجة التطبيقات**: للتكامل مع أنظمة أخرى
- **سحوبات مجدولة**: تشغيل السحب في أوقات محددة
- **إشعارات تلقائية**: إرسال النتائج عبر البريد الإلكتروني

---

## 🔍 استكشاف الأخطاء - Troubleshooting {#troubleshooting}

### المشاكل الشائعة وحلولها:

#### 1. خطأ في بدء التطبيق
**المشكلة**: البرنامج لا يبدأ أو يظهر خطأ Python
**الحل**:
```bash
# تحقق من إصدار Python
python --version

# تحقق من المكتبات المثبتة
pip list | grep pandas
pip list | grep openpyxl
pip list | grep reportlab

# إعادة تثبيت المكتبات
pip install --upgrade pandas openpyxl reportlab
```

#### 2. مشكلة في استيراد الملفات
**المشكلة**: لا يمكن استيراد ملف Excel أو CSV
**الحل**:
- تأكد من صيغة الملف (.xlsx, .xls, .csv)
- تحقق من وجود بيانات في العمود الأول
- تأكد من ترميز UTF-8 للملفات العربية
- جرب فتح الملف في Excel للتأكد من سلامته

#### 3. مشكلة في الخطوط العربية
**المشكلة**: النصوص العربية تظهر بشكل غير صحيح
**الحل**:
- تأكد من دعم النظام للخطوط العربية
- قم بتثبيت خطوط عربية إضافية
- تحقق من إعدادات اللغة في النظام

#### 4. مشكلة في إنشاء PDF
**المشكلة**: خطأ عند إنشاء تقرير PDF
**الحل**:
```bash
# إعادة تثبيت مكتبة reportlab
pip uninstall reportlab
pip install reportlab

# تحقق من صلاحيات الكتابة
# تأكد من إمكانية الكتابة في المجلد المحدد
```

#### 5. مشكلة في الأداء
**المشكلة**: البرنامج بطيء أو يتجمد
**الحل**:
- قلل عدد المشاركين في الاختبار
- زد سرعة الحركة في الإعدادات
- أغلق البرامج الأخرى لتوفير ذاكرة
- تحقق من مواصفات الجهاز

---

## ❓ الأسئلة الشائعة - FAQ {#faq}

### س1: هل يمكن استخدام البرنامج بدون إنترنت؟
**ج**: نعم، البرنامج يعمل بشكل كامل بدون إنترنت بعد تثبيت المكتبات المطلوبة.

### س2: ما هو الحد الأقصى لعدد المشاركين؟
**ج**: لا يوجد حد أقصى نظرياً، لكن للأداء الأمثل ننصح بعدم تجاوز 10,000 مشارك.

### س3: هل يمكن تخصيص شكل الواجهة؟
**ج**: نعم، يمكن تعديل الألوان والخطوط والنصوص من خلال تعديل الكود.

### س4: هل السحب عشوائي حقيقي؟
**ج**: نعم، يستخدم البرنامج مولد الأرقام العشوائية في Python والذي يعتبر عشوائي كاذب آمن للاستخدام.

### س5: هل يمكن استخدام البرنامج للأغراض التجارية؟
**ج**: البرنامج مجاني للاستخدام الشخصي والتعليمي. للاستخدام التجاري يرجى مراجعة الترخيص.

### س6: كيف يمكن إنشاء نسخة تنفيذية (.exe)؟
**ج**: يمكن استخدام PyInstaller:
```bash
pip install pyinstaller
pyinstaller --onefile --windowed electronic_raffle_system.py
```

### س7: هل يدعم البرنامج لغات أخرى غير العربية والإنجليزية؟
**ج**: حالياً يدعم العربية والإنجليزية فقط، لكن يمكن إضافة لغات أخرى بتعديل الكود.

### س8: كيف يمكن نسخ البرنامج احتياطياً؟
**ج**: انسخ المجلد كاملاً مع جميع الملفات. البيانات المهمة في:
- قوائم المشاركين (Excel/CSV)
- ملف الإعدادات (raffle_settings.json)
- التقارير المحفوظة (PDF/Excel)

---

## 📞 الدعم والمساعدة - Support

للحصول على مساعدة إضافية:
1. راجع هذا الدليل بعناية
2. تحقق من رسائل الخطأ وابحث عن الحل
3. تأكد من تثبيت جميع المتطلبات بشكل صحيح
4. جرب إعادة تشغيل البرنامج والجهاز

---

**🎉 نتمنى لكم تجربة ممتعة مع نظام السحب الإلكتروني! 🎉**
