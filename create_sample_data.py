#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to create sample data for the Electronic Raffle System
"""

import pandas as pd

# Sample Arabic and English names for testing
sample_participants = [
    "أحمد محمد علي",
    "فاطمة أحمد السالم",
    "محمد عبدالله الخالد",
    "نورا سعد المطيري",
    "عبدالرحمن فهد العتيبي",
    "سارة محمد القحطاني",
    "خالد عبدالعزيز الشمري",
    "مريم أحمد الدوسري",
    "سعد عبدالله النعيمي",
    "هند محمد الزهراني",
    "عمر فيصل الغامدي",
    "ريم سلطان العجمي",
    "يوسف عبدالرحمن الحربي",
    "لينا أحمد الفارس",
    "طارق محمد البلوي",
    "دانا عبدالله الرشيد",
    "ماجد سعود الشهري",
    "رنا فهد المالكي",
    "بدر عبدالعزيز القرني",
    "جود محمد السبيعي",
    "Ahmed Mohammed Ali",
    "Fatima Ahmed Al-Salem",
    "Mohammed Abdullah Al-Khalid",
    "Nora Saad Al-Mutairi",
    "Abdulrahman Fahd Al-Otaibi",
    "Sarah Mohammed Al-Qahtani",
    "Khalid Abdulaziz Al-Shammari",
    "Maryam Ahmed Al-Dosari",
    "Saad Abdullah Al-Nuaimi",
    "Hind Mohammed Al-Zahrani",
    "Omar Faisal Al-Ghamdi",
    "Reem Sultan Al-Ajmi",
    "Youssef Abdulrahman Al-Harbi",
    "Lina Ahmed Al-Fares",
    "Tariq Mohammed Al-Balawi",
    "Dana Abdullah Al-Rashid",
    "Majed Saud Al-Shehri",
    "Rana Fahd Al-Maliki",
    "Badr Abdulaziz Al-Qarni",
    "Joud Mohammed Al-Subai'i"
]

def create_sample_excel():
    """Create sample Excel file with participant names"""
    df = pd.DataFrame({
        'اسم المشارك - Participant Name': sample_participants
    })
    
    # Save to Excel
    df.to_excel('sample_participants.xlsx', index=False, engine='openpyxl')
    print("✅ تم إنشاء ملف sample_participants.xlsx بنجاح")
    print("✅ Sample file 'sample_participants.xlsx' created successfully")
    
    # Also create CSV version
    df.to_csv('sample_participants.csv', index=False, encoding='utf-8-sig')
    print("✅ تم إنشاء ملف sample_participants.csv بنجاح")
    print("✅ Sample file 'sample_participants.csv' created successfully")

if __name__ == "__main__":
    create_sample_excel()
