# نظام السحب الإلكتروني - Electronic Raffle System

## 🎉 مرحباً بكم في نظام السحب الإلكتروني الشامل

نظام السحب الإلكتروني هو تطبيق شامل ومتطور مصمم خصيصاً للفعاليات العربية والدولية. يوفر واجهة مستخدم جذابة وسهلة الاستخدام مع دعم كامل للغة العربية والإنجليزية.

## ✨ المميزات الرئيسية

### 🎯 واجهة المستخدم
- **تصميم جذاب ومهني** مناسب للحفلات والشركات والمدارس
- **دعم ثنائي اللغة** (العربية والإنجليزية)
- **رسوم متحركة مثيرة** أثناء عملية السحب
- **مؤثرات بصرية** عند إعلان الفائز
- **واجهة سهلة الاستخدام** للمبتدئين والمحترفين

### 📊 إدارة المشاركين
- **استيراد من Excel/CSV** - استيراد قوائم المشاركين بسهولة
- **إضافة يدوية** - إضافة المشاركين واحداً تلو الآخر
- **منع التكرار** - تجنب الأسماء المكررة تلقائياً
- **حفظ القوائم** - حفظ قوائم المشاركين والفائزين

### 🎲 نظام السحب المتقدم
- **سحب عشوائي حقيقي** باستخدام خوارزميات متقدمة
- **تحديد عدد الفائزين** - من 1 إلى 100 فائز
- **السماح بالفوز المتكرر** - خيار قابل للتخصيص
- **سرعة الحركة قابلة للتعديل** - تحكم في سرعة الرسوم المتحركة
- **عد تنازلي** قبل إعلان الفائز

### 📄 التقارير والطباعة
- **تقارير PDF احترافية** مع تفاصيل كاملة
- **تصدير إلى Excel** مع أوراق متعددة
- **طباعة النتائج** مباشرة من البرنامج
- **حفظ تلقائي** للإعدادات والنتائج

### 🔊 النظام الصوتي المتطور
- **مؤثرات صوتية متنوعة** - 9 أصوات مختلفة للتفاعل
- **أصوات الأزرار** - نقرات صوتية عند الضغط على الأزرار
- **أصوات السحب** - طبلة ودوران أثناء عملية السحب
- **أصوات الفوز** - احتفالات صوتية عند إعلان الفائزين
- **موسيقى خلفية** - أجواء هادئة أثناء الفعالية
- **تحكم كامل** - إمكانية تشغيل/إيقاف الأصوات

## 🛠️ متطلبات التشغيل

### البرامج المطلوبة
- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

### المكتبات المطلوبة
```bash
pip install pandas openpyxl reportlab pygame numpy
```

## 🚀 طريقة التشغيل

### 1. تحضير البيئة
```bash
# تثبيت المكتبات المطلوبة
pip install -r requirements.txt

# تشغيل البرنامج
python electronic_raffle_system.py
```

### 2. استخدام البرنامج

#### إضافة المشاركين
1. **من Excel/CSV**: اضغط على "استيراد من Excel" واختر الملف
2. **يدوياً**: اضغط على "إضافة يدوية" وأدخل الأسماء

#### إعداد السحب
1. حدد **عدد الفائزين** المطلوب
2. اختر **السماح بالفوز المتكرر** إذا رغبت
3. اضبط **سرعة الحركة** حسب تفضيلك

#### بدء السحب
1. اضغط على **"🎲 بدء السحب"**
2. استمتع بالرسوم المتحركة
3. شاهد إعلان الفائزين

#### حفظ النتائج
1. **عرض النتائج**: لمشاهدة تفاصيل كاملة
2. **طباعة PDF**: لحفظ تقرير احترافي
3. **حفظ Excel**: لتصدير البيانات

## 📁 الملفات المرفقة

### الملفات الأساسية
- `electronic_raffle_system.py` - البرنامج الرئيسي
- `requirements.txt` - قائمة المكتبات المطلوبة
- `create_sample_data.py` - إنشاء بيانات تجريبية

### الملفات التجريبية
- `sample_participants.xlsx` - ملف Excel تجريبي
- `sample_participants.csv` - ملف CSV تجريبي

## 🎨 أنواع الفعاليات المدعومة

### 🎉 الحفلات والمناسبات
- حفلات أعياد الميلاد
- حفلات الزفاف
- المناسبات العائلية
- الفعاليات الاجتماعية

### 🏢 الشركات والمؤسسات
- فعاليات الشركات
- المؤتمرات والندوات
- حفلات التكريم
- المسابقات المهنية

### 🎓 المدارس والجامعات
- الأنشطة المدرسية
- المسابقات الطلابية
- حفلات التخرج
- الفعاليات التعليمية

### 🌍 الفعاليات العامة
- المهرجانات
- المعارض
- الفعاليات الخيرية
- المسابقات العامة

## ⚙️ الإعدادات المتقدمة

### تخصيص الواجهة
- تغيير الألوان حسب نوع الفعالية
- إضافة شعار الجهة المنظمة
- تخصيص الرسائل والنصوص

### إعدادات السحب
- تحديد فئات الجوائز المختلفة
- ترتيب الجوائز حسب الأهمية
- إعداد مؤقت العد التنازلي

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في استيراد الملفات
- تأكد من صيغة الملف (Excel أو CSV)
- تحقق من وجود أسماء في العمود الأول
- تأكد من ترميز UTF-8 للملفات العربية

#### مشاكل في الطباعة
- تحقق من تثبيت مكتبة reportlab
- تأكد من صلاحيات الكتابة في المجلد
- جرب تشغيل البرنامج كمدير

#### مشاكل في الواجهة
- تحقق من دقة الشاشة
- جرب تغيير حجم النافذة
- تأكد من دعم النظام للخطوط العربية

## 📞 الدعم والمساعدة

### للحصول على المساعدة
- راجع هذا الدليل أولاً
- تحقق من رسائل الخطأ
- تأكد من تثبيت جميع المتطلبات

### تطوير مستقبلي
- دعم قواعد البيانات المتقدمة
- واجهة ويب تفاعلية
- تطبيق للهواتف الذكية
- دعم الصوت والموسيقى

## 📄 الترخيص

هذا البرنامج مجاني للاستخدام الشخصي والتعليمي. للاستخدام التجاري، يرجى التواصل مع المطور.

---

## 🌟 شكراً لاستخدام نظام السحب الإلكتروني!

نتمنى لكم تجربة ممتعة ومثيرة مع نظام السحب الإلكتروني. لا تترددوا في مشاركة تجاربكم وملاحظاتكم لتطوير النظام أكثر.

**🎉 حظاً موفقاً في سحوباتكم! 🎉**

## 📸 لقطات من البرنامج

### الواجهة الرئيسية
- شاشة ترحيب جذابة
- لوحة تحكم شاملة
- منطقة عرض كبيرة ووضحة

### عملية السحب
- رسوم متحركة مثيرة
- عرض الأسماء بشكل عشوائي
- إعلان الفائز بطريقة احترافية

### النتائج والتقارير
- عرض مفصل للنتائج
- تقارير PDF احترافية
- ملفات Excel منظمة

## 🎯 نصائح للاستخدام الأمثل

### قبل الفعالية
1. **جهز قائمة المشاركين** في ملف Excel مسبقاً
2. **اختبر البرنامج** مع بيانات تجريبية
3. **تأكد من جودة الصوت والعرض** في المكان
4. **احتفظ بنسخة احتياطية** من قائمة المشاركين

### أثناء الفعالية
1. **اشرح للحضور** كيفية عمل النظام
2. **استخدم الرسوم المتحركة** لزيادة الإثارة
3. **اعرض النتائج بوضوح** على شاشة كبيرة
4. **احتفظ بالهدوء** في حالة حدوث أي مشكلة تقنية

### بعد الفعالية
1. **احفظ النتائج** في ملفات PDF و Excel
2. **اطبع شهادات للفائزين** إذا لزم الأمر
3. **احتفظ بنسخة من التقرير** للسجلات
4. **شارك النتائج** مع المنظمين والمشاركين

---

*تم تطوير هذا النظام بعناية فائقة ليكون الحل الأمثل لجميع احتياجات السحب الإلكتروني في البيئة العربية والدولية.*
