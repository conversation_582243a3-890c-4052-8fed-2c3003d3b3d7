#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Electronic Raffle System - نظام السحب الإلكتروني
A comprehensive raffle system with Arabic interface support
Created for parties, companies, schools, and public events
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import random
import json
import os
from datetime import datetime
import threading
import time
from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import csv
import pygame
import platform

class ElectronicRaffleSystem:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.participants = []
        self.winners = []
        self.current_animation = False
        self.allow_repeat_winners = False
        self.animation_speed = 100  # milliseconds
        self.sound_enabled = True
        self.background_music_enabled = False
        self.setup_sound_system()
        self.setup_ui()
        self.load_settings()
        
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("نظام السحب الإلكتروني - Electronic Raffle System")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2c3e50')
        self.root.resizable(True, True)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"1000x700+{x}+{y}")

    def setup_sound_system(self):
        """Initialize the sound system"""
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            self.sound_system_available = True
            print("🔊 Sound system initialized successfully")

            # Load sound effects
            self.sounds = {}
            sound_files = {
                'button_click': 'sounds/button_click.wav',
                'raffle_start': 'sounds/raffle_start.wav',
                'rolling': 'sounds/rolling.wav',
                'winner': 'sounds/winner.wav',
                'celebration': 'sounds/celebration.wav',
                'drum_roll': 'sounds/drum_roll.wav',
                'success_chime': 'sounds/success_chime.wav',
                'error': 'sounds/error.wav',
                'background_ambient': 'sounds/background_ambient.wav'
            }

            for sound_name, file_path in sound_files.items():
                try:
                    if os.path.exists(file_path):
                        self.sounds[sound_name] = pygame.mixer.Sound(file_path)
                        print(f"✅ Loaded sound: {sound_name}")
                    else:
                        print(f"⚠️ Sound file not found: {file_path}")
                except Exception as e:
                    print(f"❌ Error loading sound {sound_name}: {e}")

        except Exception as e:
            print(f"❌ Sound system initialization failed: {e}")
            self.sound_system_available = False
            self.sound_enabled = False

    def play_sound(self, sound_name, volume=0.7):
        """Play a sound effect"""
        if not self.sound_enabled or not self.sound_system_available:
            return

        try:
            if sound_name in self.sounds:
                sound = self.sounds[sound_name]
                sound.set_volume(volume)
                sound.play()
        except Exception as e:
            print(f"❌ Error playing sound {sound_name}: {e}")

    def start_background_music(self):
        """Start background ambient music"""
        if not self.background_music_enabled or not self.sound_system_available:
            return

        try:
            if 'background_ambient' in self.sounds:
                pygame.mixer.music.load('sounds/background_ambient.wav')
                pygame.mixer.music.set_volume(0.3)
                pygame.mixer.music.play(-1)  # Loop indefinitely
        except Exception as e:
            print(f"❌ Error starting background music: {e}")

    def stop_background_music(self):
        """Stop background music"""
        try:
            pygame.mixer.music.stop()
        except Exception:
            pass
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main title
        title_frame = tk.Frame(self.root, bg='#2c3e50')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="🎉 نظام السحب الإلكتروني 🎉\nElectronic Raffle System",
            font=('Arial', 24, 'bold'),
            fg='#ecf0f1',
            bg='#2c3e50',
            justify='center'
        )
        title_label.pack()
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#34495e', relief='raised', bd=2)
        main_frame.pack(padx=20, pady=10, fill='both', expand=True)
        
        # Left panel for controls
        left_panel = tk.Frame(main_frame, bg='#34495e', width=300)
        left_panel.pack(side='left', fill='y', padx=10, pady=10)
        left_panel.pack_propagate(False)
        
        # Right panel for display
        right_panel = tk.Frame(main_frame, bg='#ecf0f1', relief='sunken', bd=2)
        right_panel.pack(side='right', fill='both', expand=True, padx=10, pady=10)
        
        self.setup_left_panel(left_panel)
        self.setup_right_panel(right_panel)
        
    def setup_left_panel(self, parent):
        """Setup control panel"""
        # Participants section
        participants_frame = tk.LabelFrame(
            parent,
            text="المشاركون - Participants",
            font=('Arial', 12, 'bold'),
            fg='#ecf0f1',
            bg='#34495e'
        )
        participants_frame.pack(fill='x', pady=5)
        
        # Import buttons
        import_frame = tk.Frame(participants_frame, bg='#34495e')
        import_frame.pack(fill='x', padx=5, pady=5)
        
        import_excel_btn = tk.Button(
            import_frame,
            text="استيراد من Excel\nImport from Excel",
            command=lambda: self.button_click_with_sound(self.import_from_excel),
            bg='#3498db',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        import_excel_btn.pack(fill='x', pady=2)
        
        manual_add_btn = tk.Button(
            import_frame,
            text="إضافة يدوية\nManual Add",
            command=lambda: self.button_click_with_sound(self.manual_add_participant),
            bg='#2ecc71',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        manual_add_btn.pack(fill='x', pady=2)
        
        # Participants count
        self.participants_count_label = tk.Label(
            participants_frame,
            text="عدد المشاركين: 0\nParticipants: 0",
            font=('Arial', 10),
            fg='#ecf0f1',
            bg='#34495e'
        )
        self.participants_count_label.pack(pady=5)
        
        # Raffle settings
        settings_frame = tk.LabelFrame(
            parent,
            text="إعدادات السحب - Raffle Settings",
            font=('Arial', 12, 'bold'),
            fg='#ecf0f1',
            bg='#34495e'
        )
        settings_frame.pack(fill='x', pady=5)
        
        # Number of winners
        tk.Label(
            settings_frame,
            text="عدد الفائزين - Winners Count:",
            font=('Arial', 10),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(anchor='w', padx=5)
        
        self.winners_count_var = tk.StringVar(value="1")
        winners_spinbox = tk.Spinbox(
            settings_frame,
            from_=1,
            to=100,
            textvariable=self.winners_count_var,
            font=('Arial', 12),
            width=10
        )
        winners_spinbox.pack(padx=5, pady=2)
        
        # Allow repeat winners
        self.repeat_winners_var = tk.BooleanVar()
        repeat_check = tk.Checkbutton(
            settings_frame,
            text="السماح بالفوز المتكرر\nAllow Repeat Winners",
            variable=self.repeat_winners_var,
            font=('Arial', 9),
            fg='#ecf0f1',
            bg='#34495e',
            selectcolor='#2c3e50'
        )
        repeat_check.pack(anchor='w', padx=5, pady=2)
        
        # Animation speed
        tk.Label(
            settings_frame,
            text="سرعة الحركة - Animation Speed:",
            font=('Arial', 10),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(anchor='w', padx=5)
        
        self.speed_var = tk.StringVar(value="100")
        speed_scale = tk.Scale(
            settings_frame,
            from_=50,
            to=500,
            orient='horizontal',
            variable=self.speed_var,
            font=('Arial', 9),
            fg='#ecf0f1',
            bg='#34495e',
            troughcolor='#2c3e50'
        )
        speed_scale.pack(fill='x', padx=5, pady=2)

        # Sound settings
        tk.Label(
            settings_frame,
            text="إعدادات الصوت - Sound Settings:",
            font=('Arial', 10),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(anchor='w', padx=5, pady=(10, 0))

        # Enable sound effects
        self.sound_enabled_var = tk.BooleanVar(value=self.sound_enabled)
        sound_check = tk.Checkbutton(
            settings_frame,
            text="تفعيل الأصوات\nEnable Sound Effects",
            variable=self.sound_enabled_var,
            command=self.toggle_sound,
            font=('Arial', 9),
            fg='#ecf0f1',
            bg='#34495e',
            selectcolor='#2c3e50'
        )
        sound_check.pack(anchor='w', padx=5, pady=2)

        # Enable background music
        self.background_music_var = tk.BooleanVar(value=self.background_music_enabled)
        music_check = tk.Checkbutton(
            settings_frame,
            text="موسيقى خلفية\nBackground Music",
            variable=self.background_music_var,
            command=self.toggle_background_music,
            font=('Arial', 9),
            fg='#ecf0f1',
            bg='#34495e',
            selectcolor='#2c3e50'
        )
        music_check.pack(anchor='w', padx=5, pady=2)

    def toggle_sound(self):
        """Toggle sound effects on/off"""
        self.sound_enabled = self.sound_enabled_var.get()
        if self.sound_enabled:
            self.play_sound('success_chime', 0.5)
        print(f"🔊 Sound effects: {'ON' if self.sound_enabled else 'OFF'}")

    def toggle_background_music(self):
        """Toggle background music on/off"""
        self.background_music_enabled = self.background_music_var.get()
        if self.background_music_enabled:
            self.start_background_music()
        else:
            self.stop_background_music()
        print(f"🎵 Background music: {'ON' if self.background_music_enabled else 'OFF'}")

    def button_click_with_sound(self, callback):
        """Execute button callback with click sound"""
        self.play_sound('button_click', 0.4)
        callback()
        
        # Action buttons
        actions_frame = tk.LabelFrame(
            parent,
            text="الإجراءات - Actions",
            font=('Arial', 12, 'bold'),
            fg='#ecf0f1',
            bg='#34495e'
        )
        actions_frame.pack(fill='x', pady=5)
        
        self.start_button = tk.Button(
            actions_frame,
            text="🎲 بدء السحب\nStart Raffle",
            command=lambda: self.button_click_with_sound(self.start_raffle),
            bg='#e74c3c',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='raised',
            bd=3,
            height=2
        )
        self.start_button.pack(fill='x', padx=5, pady=5)
        
        show_results_btn = tk.Button(
            actions_frame,
            text="📊 عرض النتائج\nShow Results",
            command=lambda: self.button_click_with_sound(self.show_results),
            bg='#9b59b6',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        show_results_btn.pack(fill='x', padx=5, pady=2)

        pdf_btn = tk.Button(
            actions_frame,
            text="📄 طباعة PDF\nPrint PDF",
            command=lambda: self.button_click_with_sound(self.generate_pdf_report),
            bg='#f39c12',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        pdf_btn.pack(fill='x', padx=5, pady=2)

        reset_btn = tk.Button(
            actions_frame,
            text="🔄 إعادة تعيين\nReset",
            command=lambda: self.button_click_with_sound(self.reset_raffle),
            bg='#95a5a6',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        reset_btn.pack(fill='x', padx=5, pady=2)
        
    def setup_right_panel(self, parent):
        """Setup display panel"""
        # Display title
        display_title = tk.Label(
            parent,
            text="🎯 شاشة العرض - Display Screen 🎯",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50',
            bg='#ecf0f1'
        )
        display_title.pack(pady=10)
        
        # Main display area
        self.display_frame = tk.Frame(parent, bg='#ecf0f1')
        self.display_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Current name display
        self.current_name_label = tk.Label(
            self.display_frame,
            text="مرحباً بكم في نظام السحب الإلكتروني\nWelcome to Electronic Raffle System",
            font=('Arial', 20, 'bold'),
            fg='#2c3e50',
            bg='#ecf0f1',
            wraplength=400,
            justify='center'
        )
        self.current_name_label.pack(expand=True)
        
        # Winners display
        self.winners_display = tk.Text(
            self.display_frame,
            height=8,
            font=('Arial', 12),
            bg='#ffffff',
            fg='#2c3e50',
            relief='sunken',
            bd=2,
            wrap='word'
        )
        self.winners_display.pack(fill='x', pady=10)
        self.winners_display.insert('1.0', "الفائزون سيظهرون هنا\nWinners will appear here")
        self.winners_display.config(state='disabled')

    def import_from_excel(self):
        """Import participants from Excel file"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Excel - Select Excel File",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                if file_path.endswith('.csv'):
                    df = pd.read_csv(file_path, encoding='utf-8')
                else:
                    df = pd.read_excel(file_path)

                # Assume first column contains names
                names = df.iloc[:, 0].dropna().tolist()
                self.participants = [str(name).strip() for name in names if str(name).strip()]

                self.update_participants_count()
                self.play_sound('success_chime', 0.6)
                messagebox.showinfo(
                    "نجح الاستيراد - Import Successful",
                    f"تم استيراد {len(self.participants)} مشارك بنجاح\n"
                    f"Successfully imported {len(self.participants)} participants"
                )

            except Exception as e:
                self.play_sound('error', 0.6)
                messagebox.showerror(
                    "خطأ في الاستيراد - Import Error",
                    f"حدث خطأ أثناء استيراد الملف:\n{str(e)}"
                )

    def manual_add_participant(self):
        """Manually add a participant"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة مشارك - Add Participant")
        dialog.geometry("400x200")
        dialog.configure(bg='#34495e')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (200 // 2)
        dialog.geometry(f"400x200+{x}+{y}")

        tk.Label(
            dialog,
            text="اسم المشارك - Participant Name:",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(pady=10)

        name_entry = tk.Entry(dialog, font=('Arial', 12), width=30)
        name_entry.pack(pady=5)
        name_entry.focus()

        def add_name():
            name = name_entry.get().strip()
            if name:
                if name not in self.participants:
                    self.participants.append(name)
                    self.update_participants_count()
                    messagebox.showinfo("تم الإضافة - Added", f"تم إضافة: {name}")
                    dialog.destroy()
                else:
                    messagebox.showwarning("تحذير - Warning", "هذا الاسم موجود بالفعل\nThis name already exists")
            else:
                messagebox.showwarning("تحذير - Warning", "يرجى إدخال اسم صحيح\nPlease enter a valid name")

        tk.Button(
            dialog,
            text="إضافة - Add",
            command=add_name,
            bg='#2ecc71',
            fg='white',
            font=('Arial', 12),
            width=15
        ).pack(pady=10)

        # Bind Enter key to add name
        name_entry.bind('<Return>', lambda e: add_name())

    def update_participants_count(self):
        """Update participants count display"""
        count = len(self.participants)
        self.participants_count_label.config(
            text=f"عدد المشاركين: {count}\nParticipants: {count}"
        )

    def start_raffle(self):
        """Start the raffle animation and selection"""
        if not self.participants:
            self.play_sound('error', 0.6)
            messagebox.showwarning(
                "تحذير - Warning",
                "لا يوجد مشاركون!\nNo participants found!"
            )
            return

        try:
            winners_count = int(self.winners_count_var.get())
        except ValueError:
            self.play_sound('error', 0.6)
            messagebox.showerror("خطأ - Error", "يرجى إدخال عدد صحيح للفائزين\nPlease enter a valid number of winners")
            return

        if winners_count > len(self.participants) and not self.repeat_winners_var.get():
            self.play_sound('error', 0.6)
            messagebox.showwarning(
                "تحذير - Warning",
                "عدد الفائزين أكبر من عدد المشاركين!\nNumber of winners exceeds participants!"
            )
            return

        # Play raffle start sound
        self.play_sound('raffle_start', 0.8)

        self.current_animation = True
        self.start_button.config(state='disabled', text="جاري السحب...\nDrawing...")

        # Start animation in separate thread
        animation_thread = threading.Thread(target=self.animate_raffle, args=(winners_count,))
        animation_thread.daemon = True
        animation_thread.start()

    def animate_raffle(self, winners_count):
        """Animate the raffle selection"""
        self.animation_speed = int(self.speed_var.get())

        # Clear previous winners display
        self.winners_display.config(state='normal')
        self.winners_display.delete('1.0', 'end')
        self.winners_display.config(state='disabled')

        selected_winners = []
        available_participants = self.participants.copy()

        # Play drum roll sound at the beginning
        self.play_sound('drum_roll', 0.6)

        for winner_num in range(winners_count):
            if not available_participants and not self.repeat_winners_var.get():
                break

            # Play rolling sound for this winner
            self.play_sound('rolling', 0.4)

            # Animation phase
            for i in range(30):  # 30 animation cycles
                if not self.current_animation:
                    break

                random_name = random.choice(self.participants)
                self.current_name_label.config(
                    text=f"🎲 {random_name} 🎲",
                    font=('Arial', 24, 'bold'),
                    fg='#e74c3c'
                )
                self.root.update()
                time.sleep(self.animation_speed / 1000.0)

            # Select winner
            if self.repeat_winners_var.get():
                winner = random.choice(self.participants)
            else:
                winner = random.choice(available_participants)
                available_participants.remove(winner)

            selected_winners.append(winner)

            # Play winner sound
            self.play_sound('winner', 0.8)

            # Display winner
            self.current_name_label.config(
                text=f"🏆 الفائز {winner_num + 1}: {winner} 🏆\nWinner {winner_num + 1}: {winner}",
                font=('Arial', 20, 'bold'),
                fg='#27ae60'
            )

            # Update winners display
            self.winners_display.config(state='normal')
            self.winners_display.insert('end', f"🏆 الفائز {winner_num + 1}: {winner}\n")
            self.winners_display.config(state='disabled')
            self.winners_display.see('end')

            self.root.update()
            time.sleep(1)  # Pause between winners

        self.winners = selected_winners
        self.current_animation = False
        self.start_button.config(state='normal', text="🎲 بدء السحب\nStart Raffle")

        # Play celebration sound
        self.play_sound('celebration', 0.9)

        # Show completion message
        self.play_sound('success_chime', 0.7)
        messagebox.showinfo(
            "انتهى السحب - Raffle Complete",
            f"تم اختيار {len(selected_winners)} فائز\n"
            f"Selected {len(selected_winners)} winners"
        )

    def show_results(self):
        """Show detailed results window"""
        if not self.winners:
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        results_window = tk.Toplevel(self.root)
        results_window.title("نتائج السحب - Raffle Results")
        results_window.geometry("600x500")
        results_window.configure(bg='#ecf0f1')
        results_window.transient(self.root)

        # Center the window
        results_window.update_idletasks()
        x = (results_window.winfo_screenwidth() // 2) - (600 // 2)
        y = (results_window.winfo_screenheight() // 2) - (500 // 2)
        results_window.geometry(f"600x500+{x}+{y}")

        # Title
        tk.Label(
            results_window,
            text="🏆 نتائج السحب - Raffle Results 🏆",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50',
            bg='#ecf0f1'
        ).pack(pady=10)

        # Results text area
        results_text = tk.Text(
            results_window,
            font=('Arial', 12),
            bg='#ffffff',
            fg='#2c3e50',
            relief='sunken',
            bd=2,
            wrap='word'
        )
        results_text.pack(fill='both', expand=True, padx=20, pady=10)

        # Add results content
        results_content = f"تاريخ السحب - Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        results_content += f"عدد المشاركين - Total Participants: {len(self.participants)}\n"
        results_content += f"عدد الفائزين - Number of Winners: {len(self.winners)}\n"
        results_content += "=" * 50 + "\n\n"

        for i, winner in enumerate(self.winners, 1):
            results_content += f"🏆 الفائز {i} - Winner {i}: {winner}\n"

        results_content += "\n" + "=" * 50 + "\n"
        results_content += "جميع المشاركين - All Participants:\n"
        for i, participant in enumerate(self.participants, 1):
            results_content += f"{i}. {participant}\n"

        results_text.insert('1.0', results_content)
        results_text.config(state='disabled')

        # Buttons frame
        buttons_frame = tk.Frame(results_window, bg='#ecf0f1')
        buttons_frame.pack(pady=10)

        tk.Button(
            buttons_frame,
            text="حفظ كـ Excel\nSave as Excel",
            command=self.export_to_excel,
            bg='#27ae60',
            fg='white',
            font=('Arial', 10),
            width=15
        ).pack(side='left', padx=5)

        tk.Button(
            buttons_frame,
            text="إغلاق\nClose",
            command=results_window.destroy,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 10),
            width=15
        ).pack(side='right', padx=5)

    def generate_pdf_report(self):
        """Generate PDF report of raffle results"""
        if not self.winners:
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        try:
            filename = f"raffle_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                initialname=filename,
                title="حفظ تقرير PDF - Save PDF Report"
            )

            if not filepath:
                return

            # Create PDF
            c = canvas.Canvas(filepath, pagesize=A4)
            width, height = A4

            # Title
            c.setFont("Helvetica-Bold", 20)
            c.drawCentredText(width/2, height-50, "Electronic Raffle System Results")
            c.drawCentredText(width/2, height-75, "نتائج نظام السحب الإلكتروني")

            # Date and info
            c.setFont("Helvetica", 12)
            y_position = height - 120
            c.drawString(50, y_position, f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            y_position -= 20
            c.drawString(50, y_position, f"Total Participants: {len(self.participants)}")
            y_position -= 20
            c.drawString(50, y_position, f"Number of Winners: {len(self.winners)}")
            y_position -= 40

            # Winners section
            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, y_position, "Winners - الفائزون:")
            y_position -= 25

            c.setFont("Helvetica", 12)
            for i, winner in enumerate(self.winners, 1):
                if y_position < 100:  # Start new page if needed
                    c.showPage()
                    y_position = height - 50
                c.drawString(70, y_position, f"{i}. {winner}")
                y_position -= 20

            y_position -= 20

            # Participants section
            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, y_position, "All Participants - جميع المشاركين:")
            y_position -= 25

            c.setFont("Helvetica", 10)
            for i, participant in enumerate(self.participants, 1):
                if y_position < 100:  # Start new page if needed
                    c.showPage()
                    y_position = height - 50
                c.drawString(70, y_position, f"{i}. {participant}")
                y_position -= 15

            c.save()

            messagebox.showinfo(
                "تم الحفظ - Saved Successfully",
                f"تم حفظ التقرير بنجاح:\n{filepath}"
            )

        except Exception as e:
            messagebox.showerror(
                "خطأ في الحفظ - Save Error",
                f"حدث خطأ أثناء حفظ التقرير:\n{str(e)}"
            )

    def export_to_excel(self):
        """Export results to Excel file"""
        if not self.winners:
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        try:
            filename = f"raffle_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            filepath = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                initialname=filename,
                title="حفظ ملف Excel - Save Excel File"
            )

            if not filepath:
                return

            # Create DataFrame with results
            data = {
                'Winner Position': [f"Winner {i+1}" for i in range(len(self.winners))],
                'Winner Name': self.winners,
                'Draw Date': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')] * len(self.winners)
            }

            df_winners = pd.DataFrame(data)

            # Create DataFrame with all participants
            df_participants = pd.DataFrame({
                'Participant Number': range(1, len(self.participants) + 1),
                'Participant Name': self.participants
            })

            # Save to Excel with multiple sheets
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df_winners.to_excel(writer, sheet_name='Winners', index=False)
                df_participants.to_excel(writer, sheet_name='All Participants', index=False)

            messagebox.showinfo(
                "تم الحفظ - Saved Successfully",
                f"تم حفظ الملف بنجاح:\n{filepath}"
            )

        except Exception as e:
            messagebox.showerror(
                "خطأ في الحفظ - Save Error",
                f"حدث خطأ أثناء حفظ الملف:\n{str(e)}"
            )

    def reset_raffle(self):
        """Reset the raffle system"""
        result = messagebox.askyesno(
            "تأكيد إعادة التعيين - Confirm Reset",
            "هل أنت متأكد من إعادة تعيين النظام؟\nسيتم حذف جميع البيانات!\n\n"
            "Are you sure you want to reset the system?\nAll data will be deleted!"
        )

        if result:
            self.participants = []
            self.winners = []
            self.current_animation = False

            # Reset UI
            self.update_participants_count()
            self.current_name_label.config(
                text="مرحباً بكم في نظام السحب الإلكتروني\nWelcome to Electronic Raffle System",
                font=('Arial', 20, 'bold'),
                fg='#2c3e50'
            )

            self.winners_display.config(state='normal')
            self.winners_display.delete('1.0', 'end')
            self.winners_display.insert('1.0', "الفائزون سيظهرون هنا\nWinners will appear here")
            self.winners_display.config(state='disabled')

            self.start_button.config(state='normal', text="🎲 بدء السحب\nStart Raffle")

            messagebox.showinfo(
                "تم إعادة التعيين - Reset Complete",
                "تم إعادة تعيين النظام بنجاح\nSystem reset successfully"
            )

    def load_settings(self):
        """Load settings from file"""
        try:
            if os.path.exists('raffle_settings.json'):
                with open('raffle_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.winners_count_var.set(str(settings.get('winners_count', 1)))
                    self.repeat_winners_var.set(settings.get('allow_repeat', False))
                    self.speed_var.set(str(settings.get('animation_speed', 100)))
                    self.sound_enabled = settings.get('sound_enabled', True)
                    self.background_music_enabled = settings.get('background_music_enabled', False)

                    # Update UI variables if they exist
                    if hasattr(self, 'sound_enabled_var'):
                        self.sound_enabled_var.set(self.sound_enabled)
                    if hasattr(self, 'background_music_var'):
                        self.background_music_var.set(self.background_music_enabled)
        except Exception:
            pass  # Use default settings if loading fails

    def save_settings(self):
        """Save settings to file"""
        try:
            settings = {
                'winners_count': int(self.winners_count_var.get()),
                'allow_repeat': self.repeat_winners_var.get(),
                'animation_speed': int(self.speed_var.get()),
                'sound_enabled': self.sound_enabled,
                'background_music_enabled': self.background_music_enabled
            }
            with open('raffle_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception:
            pass  # Ignore save errors

    def on_closing(self):
        """Handle window closing"""
        self.save_settings()
        if self.current_animation:
            self.current_animation = False
        self.root.destroy()

    def run(self):
        """Run the application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """Main function to run the Electronic Raffle System"""
    try:
        app = ElectronicRaffleSystem()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("خطأ في التطبيق - Application Error", f"حدث خطأ في بدء التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
