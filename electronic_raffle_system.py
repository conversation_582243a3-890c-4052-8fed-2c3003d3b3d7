#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Electronic Raffle System - نظام السحب الإلكتروني
A comprehensive raffle system with Arabic interface support
Created for parties, companies, schools, and public events
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import random
import json
import os
from datetime import datetime
import threading
import time
from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import csv
import pygame
import platform
from report_templates import BasicReportTemplate, DetailedReportTemplate, CertificateTemplate

class ElectronicRaffleSystem:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.participants = []
        self.winners = []
        self.current_animation = False
        self.allow_repeat_winners = False
        self.animation_speed = 100  # milliseconds
        self.sound_enabled = True
        self.background_music_enabled = False

        # Advanced features
        self.prize_categories = []
        self.current_category = 0
        self.raffle_history = []
        self.statistics = {
            'total_raffles': 0,
            'total_participants': 0,
            'total_winners': 0,
            'last_raffle_date': None
        }
        self.setup_sound_system()
        self.setup_ui()
        self.load_settings()
        
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("نظام السحب الإلكتروني - Electronic Raffle System")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2c3e50')
        self.root.resizable(True, True)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"1000x700+{x}+{y}")

    def setup_sound_system(self):
        """Initialize the sound system"""
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            self.sound_system_available = True
            print("🔊 Sound system initialized successfully")

            # Load sound effects
            self.sounds = {}
            sound_files = {
                'button_click': 'sounds/button_click.wav',
                'raffle_start': 'sounds/raffle_start.wav',
                'rolling': 'sounds/rolling.wav',
                'winner': 'sounds/winner.wav',
                'celebration': 'sounds/celebration.wav',
                'drum_roll': 'sounds/drum_roll.wav',
                'success_chime': 'sounds/success_chime.wav',
                'error': 'sounds/error.wav',
                'background_ambient': 'sounds/background_ambient.wav'
            }

            for sound_name, file_path in sound_files.items():
                try:
                    if os.path.exists(file_path):
                        self.sounds[sound_name] = pygame.mixer.Sound(file_path)
                        print(f"✅ Loaded sound: {sound_name}")
                    else:
                        print(f"⚠️ Sound file not found: {file_path}")
                except Exception as e:
                    print(f"❌ Error loading sound {sound_name}: {e}")

        except Exception as e:
            print(f"❌ Sound system initialization failed: {e}")
            self.sound_system_available = False
            self.sound_enabled = False

    def play_sound(self, sound_name, volume=0.7):
        """Play a sound effect"""
        if not self.sound_enabled or not self.sound_system_available:
            return

        try:
            if sound_name in self.sounds:
                sound = self.sounds[sound_name]
                sound.set_volume(volume)
                sound.play()
        except Exception as e:
            print(f"❌ Error playing sound {sound_name}: {e}")

    def start_background_music(self):
        """Start background ambient music"""
        if not self.background_music_enabled or not self.sound_system_available:
            return

        try:
            if 'background_ambient' in self.sounds:
                pygame.mixer.music.load('sounds/background_ambient.wav')
                pygame.mixer.music.set_volume(0.3)
                pygame.mixer.music.play(-1)  # Loop indefinitely
        except Exception as e:
            print(f"❌ Error starting background music: {e}")

    def stop_background_music(self):
        """Stop background music"""
        try:
            pygame.mixer.music.stop()
        except Exception:
            pass
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main title
        title_frame = tk.Frame(self.root, bg='#2c3e50')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="🎉 نظام السحب الإلكتروني 🎉\nElectronic Raffle System",
            font=('Arial', 24, 'bold'),
            fg='#ecf0f1',
            bg='#2c3e50',
            justify='center'
        )
        title_label.pack()
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#34495e', relief='raised', bd=2)
        main_frame.pack(padx=20, pady=10, fill='both', expand=True)
        
        # Left panel for controls
        left_panel = tk.Frame(main_frame, bg='#34495e', width=300)
        left_panel.pack(side='left', fill='y', padx=10, pady=10)
        left_panel.pack_propagate(False)
        
        # Right panel for display
        right_panel = tk.Frame(main_frame, bg='#ecf0f1', relief='sunken', bd=2)
        right_panel.pack(side='right', fill='both', expand=True, padx=10, pady=10)
        
        self.setup_left_panel(left_panel)
        self.setup_right_panel(right_panel)
        
    def setup_left_panel(self, parent):
        """Setup control panel"""
        # Participants section
        participants_frame = tk.LabelFrame(
            parent,
            text="المشاركون - Participants",
            font=('Arial', 12, 'bold'),
            fg='#ecf0f1',
            bg='#34495e'
        )
        participants_frame.pack(fill='x', pady=5)
        
        # Import buttons
        import_frame = tk.Frame(participants_frame, bg='#34495e')
        import_frame.pack(fill='x', padx=5, pady=5)
        
        import_excel_btn = tk.Button(
            import_frame,
            text="استيراد من Excel\nImport from Excel",
            command=lambda: self.button_click_with_sound(self.import_from_excel),
            bg='#3498db',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        import_excel_btn.pack(fill='x', pady=2)
        
        manual_add_btn = tk.Button(
            import_frame,
            text="إضافة يدوية\nManual Add",
            command=lambda: self.button_click_with_sound(self.manual_add_participant),
            bg='#2ecc71',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        manual_add_btn.pack(fill='x', pady=2)
        
        # Participants count
        self.participants_count_label = tk.Label(
            participants_frame,
            text="عدد المشاركين: 0\nParticipants: 0",
            font=('Arial', 10),
            fg='#ecf0f1',
            bg='#34495e'
        )
        self.participants_count_label.pack(pady=5)
        
        # Raffle settings
        settings_frame = tk.LabelFrame(
            parent,
            text="إعدادات السحب - Raffle Settings",
            font=('Arial', 12, 'bold'),
            fg='#ecf0f1',
            bg='#34495e'
        )
        settings_frame.pack(fill='x', pady=5)
        
        # Number of winners
        tk.Label(
            settings_frame,
            text="عدد الفائزين - Winners Count:",
            font=('Arial', 10),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(anchor='w', padx=5)
        
        self.winners_count_var = tk.StringVar(value="1")
        winners_spinbox = tk.Spinbox(
            settings_frame,
            from_=1,
            to=100,
            textvariable=self.winners_count_var,
            font=('Arial', 12),
            width=10
        )
        winners_spinbox.pack(padx=5, pady=2)
        
        # Allow repeat winners
        self.repeat_winners_var = tk.BooleanVar()
        repeat_check = tk.Checkbutton(
            settings_frame,
            text="السماح بالفوز المتكرر\nAllow Repeat Winners",
            variable=self.repeat_winners_var,
            font=('Arial', 9),
            fg='#ecf0f1',
            bg='#34495e',
            selectcolor='#2c3e50'
        )
        repeat_check.pack(anchor='w', padx=5, pady=2)
        
        # Animation speed
        tk.Label(
            settings_frame,
            text="سرعة الحركة - Animation Speed:",
            font=('Arial', 10),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(anchor='w', padx=5)
        
        self.speed_var = tk.StringVar(value="100")
        speed_scale = tk.Scale(
            settings_frame,
            from_=50,
            to=500,
            orient='horizontal',
            variable=self.speed_var,
            font=('Arial', 9),
            fg='#ecf0f1',
            bg='#34495e',
            troughcolor='#2c3e50'
        )
        speed_scale.pack(fill='x', padx=5, pady=2)

        # Sound settings
        tk.Label(
            settings_frame,
            text="إعدادات الصوت - Sound Settings:",
            font=('Arial', 10),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(anchor='w', padx=5, pady=(10, 0))

        # Enable sound effects
        self.sound_enabled_var = tk.BooleanVar(value=self.sound_enabled)
        sound_check = tk.Checkbutton(
            settings_frame,
            text="تفعيل الأصوات\nEnable Sound Effects",
            variable=self.sound_enabled_var,
            command=self.toggle_sound,
            font=('Arial', 9),
            fg='#ecf0f1',
            bg='#34495e',
            selectcolor='#2c3e50'
        )
        sound_check.pack(anchor='w', padx=5, pady=2)

        # Enable background music
        self.background_music_var = tk.BooleanVar(value=self.background_music_enabled)
        music_check = tk.Checkbutton(
            settings_frame,
            text="موسيقى خلفية\nBackground Music",
            variable=self.background_music_var,
            command=self.toggle_background_music,
            font=('Arial', 9),
            fg='#ecf0f1',
            bg='#34495e',
            selectcolor='#2c3e50'
        )
        music_check.pack(anchor='w', padx=5, pady=2)

        # Prize categories section
        categories_frame = tk.LabelFrame(
            parent,
            text="فئات الجوائز - Prize Categories",
            font=('Arial', 12, 'bold'),
            fg='#ecf0f1',
            bg='#34495e'
        )
        categories_frame.pack(fill='x', pady=5)

        # Add category button
        add_category_btn = tk.Button(
            categories_frame,
            text="إضافة فئة جائزة\nAdd Prize Category",
            command=lambda: self.button_click_with_sound(self.add_prize_category),
            bg='#e67e22',
            fg='white',
            font=('Arial', 9),
            relief='raised',
            bd=2
        )
        add_category_btn.pack(fill='x', padx=5, pady=2)

        # Categories list
        self.categories_listbox = tk.Listbox(
            categories_frame,
            height=3,
            font=('Arial', 9),
            bg='#ffffff',
            fg='#2c3e50'
        )
        self.categories_listbox.pack(fill='x', padx=5, pady=2)

        # Category management buttons
        cat_buttons_frame = tk.Frame(categories_frame, bg='#34495e')
        cat_buttons_frame.pack(fill='x', padx=5, pady=2)

        edit_cat_btn = tk.Button(
            cat_buttons_frame,
            text="تعديل\nEdit",
            command=lambda: self.button_click_with_sound(self.edit_prize_category),
            bg='#3498db',
            fg='white',
            font=('Arial', 8),
            width=8
        )
        edit_cat_btn.pack(side='left', padx=2)

        delete_cat_btn = tk.Button(
            cat_buttons_frame,
            text="حذف\nDelete",
            command=lambda: self.button_click_with_sound(self.delete_prize_category),
            bg='#e74c3c',
            fg='white',
            font=('Arial', 8),
            width=8
        )
        delete_cat_btn.pack(side='right', padx=2)

    def toggle_sound(self):
        """Toggle sound effects on/off"""
        self.sound_enabled = self.sound_enabled_var.get()
        if self.sound_enabled:
            self.play_sound('success_chime', 0.5)
        print(f"🔊 Sound effects: {'ON' if self.sound_enabled else 'OFF'}")

    def toggle_background_music(self):
        """Toggle background music on/off"""
        self.background_music_enabled = self.background_music_var.get()
        if self.background_music_enabled:
            self.start_background_music()
        else:
            self.stop_background_music()
        print(f"🎵 Background music: {'ON' if self.background_music_enabled else 'OFF'}")

    def button_click_with_sound(self, callback):
        """Execute button callback with click sound"""
        self.play_sound('button_click', 0.4)
        callback()

    def add_prize_category(self):
        """Add a new prize category"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة فئة جائزة - Add Prize Category")
        dialog.geometry("500x300")
        dialog.configure(bg='#34495e')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f"500x300+{x}+{y}")

        # Category name
        tk.Label(
            dialog,
            text="اسم الفئة - Category Name:",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(pady=10)

        name_entry = tk.Entry(dialog, font=('Arial', 12), width=40)
        name_entry.pack(pady=5)
        name_entry.focus()

        # Prize description
        tk.Label(
            dialog,
            text="وصف الجائزة - Prize Description:",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(pady=(10, 5))

        desc_text = tk.Text(dialog, height=4, width=50, font=('Arial', 10))
        desc_text.pack(pady=5)

        # Number of winners
        tk.Label(
            dialog,
            text="عدد الفائزين - Number of Winners:",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(pady=(10, 5))

        winners_spinbox = tk.Spinbox(
            dialog,
            from_=1,
            to=50,
            font=('Arial', 12),
            width=10
        )
        winners_spinbox.pack(pady=5)
        winners_spinbox.set("1")

        def save_category():
            name = name_entry.get().strip()
            description = desc_text.get("1.0", "end-1c").strip()
            winners_count = int(winners_spinbox.get())

            if name:
                category = {
                    'name': name,
                    'description': description,
                    'winners_count': winners_count,
                    'winners': []
                }
                self.prize_categories.append(category)
                self.update_categories_display()
                self.play_sound('success_chime', 0.6)
                messagebox.showinfo("تم الإضافة - Added", f"تم إضافة فئة: {name}")
                dialog.destroy()
            else:
                self.play_sound('error', 0.6)
                messagebox.showwarning("تحذير - Warning", "يرجى إدخال اسم الفئة\nPlease enter category name")

        # Buttons
        buttons_frame = tk.Frame(dialog, bg='#34495e')
        buttons_frame.pack(pady=20)

        tk.Button(
            buttons_frame,
            text="حفظ - Save",
            command=save_category,
            bg='#2ecc71',
            fg='white',
            font=('Arial', 12),
            width=15
        ).pack(side='left', padx=10)

        tk.Button(
            buttons_frame,
            text="إلغاء - Cancel",
            command=dialog.destroy,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 12),
            width=15
        ).pack(side='right', padx=10)

    def update_categories_display(self):
        """Update the categories listbox"""
        self.categories_listbox.delete(0, tk.END)
        for i, category in enumerate(self.prize_categories):
            display_text = f"{i+1}. {category['name']} ({category['winners_count']} فائز)"
            self.categories_listbox.insert(tk.END, display_text)

    def edit_prize_category(self):
        """Edit selected prize category"""
        selection = self.categories_listbox.curselection()
        if not selection:
            self.play_sound('error', 0.6)
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار فئة للتعديل\nPlease select a category to edit")
            return

        index = selection[0]
        category = self.prize_categories[index]

        # Similar dialog to add_prize_category but with pre-filled values
        # Implementation would be similar to add_prize_category
        messagebox.showinfo("قريباً - Coming Soon", "ميزة التعديل ستكون متاحة قريباً\nEdit feature coming soon")

    def delete_prize_category(self):
        """Delete selected prize category"""
        selection = self.categories_listbox.curselection()
        if not selection:
            self.play_sound('error', 0.6)
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار فئة للحذف\nPlease select a category to delete")
            return

        index = selection[0]
        category = self.prize_categories[index]

        result = messagebox.askyesno(
            "تأكيد الحذف - Confirm Delete",
            f"هل أنت متأكد من حذف فئة '{category['name']}'؟\n"
            f"Are you sure you want to delete category '{category['name']}'?"
        )

        if result:
            del self.prize_categories[index]
            self.update_categories_display()
            self.play_sound('success_chime', 0.6)
            messagebox.showinfo("تم الحذف - Deleted", "تم حذف الفئة بنجاح\nCategory deleted successfully")

    def show_statistics(self):
        """Show detailed statistics window"""
        stats_window = tk.Toplevel(self.root)
        stats_window.title("الإحصائيات - Statistics")
        stats_window.geometry("700x600")
        stats_window.configure(bg='#ecf0f1')
        stats_window.transient(self.root)

        # Center the window
        stats_window.update_idletasks()
        x = (stats_window.winfo_screenwidth() // 2) - (700 // 2)
        y = (stats_window.winfo_screenheight() // 2) - (600 // 2)
        stats_window.geometry(f"700x600+{x}+{y}")

        # Title
        tk.Label(
            stats_window,
            text="📈 إحصائيات النظام - System Statistics 📈",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50',
            bg='#ecf0f1'
        ).pack(pady=10)

        # Create notebook for tabs
        notebook = ttk.Notebook(stats_window)
        notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # General statistics tab
        general_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(general_frame, text="إحصائيات عامة - General")

        # Current session stats
        current_frame = tk.LabelFrame(
            general_frame,
            text="الجلسة الحالية - Current Session",
            font=('Arial', 12, 'bold'),
            bg='#ffffff'
        )
        current_frame.pack(fill='x', padx=10, pady=10)

        current_stats = f"""
عدد المشاركين الحالي: {len(self.participants)}
عدد الفائزين: {len(self.winners)}
عدد فئات الجوائز: {len(self.prize_categories)}
حالة الصوت: {'مفعل' if self.sound_enabled else 'معطل'}
الموسيقى الخلفية: {'مفعلة' if self.background_music_enabled else 'معطلة'}

Current Participants: {len(self.participants)}
Winners Selected: {len(self.winners)}
Prize Categories: {len(self.prize_categories)}
Sound: {'Enabled' if self.sound_enabled else 'Disabled'}
Background Music: {'Enabled' if self.background_music_enabled else 'Disabled'}
        """

        tk.Label(
            current_frame,
            text=current_stats,
            font=('Arial', 10),
            bg='#ffffff',
            justify='left'
        ).pack(padx=10, pady=10)

        # Historical stats
        history_frame = tk.LabelFrame(
            general_frame,
            text="الإحصائيات التاريخية - Historical Statistics",
            font=('Arial', 12, 'bold'),
            bg='#ffffff'
        )
        history_frame.pack(fill='x', padx=10, pady=10)

        historical_stats = f"""
إجمالي السحوبات: {self.statistics['total_raffles']}
إجمالي المشاركين: {self.statistics['total_participants']}
إجمالي الفائزين: {self.statistics['total_winners']}
آخر سحب: {self.statistics['last_raffle_date'] or 'لم يتم إجراء سحب بعد'}

Total Raffles: {self.statistics['total_raffles']}
Total Participants: {self.statistics['total_participants']}
Total Winners: {self.statistics['total_winners']}
Last Raffle: {self.statistics['last_raffle_date'] or 'No raffle conducted yet'}
        """

        tk.Label(
            history_frame,
            text=historical_stats,
            font=('Arial', 10),
            bg='#ffffff',
            justify='left'
        ).pack(padx=10, pady=10)

        # Prize categories tab
        categories_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(categories_frame, text="فئات الجوائز - Categories")

        if self.prize_categories:
            categories_text = tk.Text(
                categories_frame,
                font=('Arial', 10),
                bg='#ffffff',
                wrap='word'
            )
            categories_text.pack(fill='both', expand=True, padx=10, pady=10)

            categories_content = "فئات الجوائز المتاحة - Available Prize Categories:\n\n"
            for i, category in enumerate(self.prize_categories, 1):
                categories_content += f"{i}. {category['name']}\n"
                categories_content += f"   الوصف: {category['description']}\n"
                categories_content += f"   عدد الفائزين: {category['winners_count']}\n"
                categories_content += f"   الفائزون: {len(category['winners'])}\n\n"

            categories_text.insert('1.0', categories_content)
            categories_text.config(state='disabled')
        else:
            tk.Label(
                categories_frame,
                text="لا توجد فئات جوائز محددة\nNo prize categories defined",
                font=('Arial', 14),
                bg='#ffffff',
                fg='#7f8c8d'
            ).pack(expand=True)

        # Close button
        tk.Button(
            stats_window,
            text="إغلاق - Close",
            command=stats_window.destroy,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 12),
            width=20
        ).pack(pady=10)

    def show_raffle_history(self):
        """Show raffle history window"""
        history_window = tk.Toplevel(self.root)
        history_window.title("سجل السحوبات - Raffle History")
        history_window.geometry("800x500")
        history_window.configure(bg='#ecf0f1')
        history_window.transient(self.root)

        # Center the window
        history_window.update_idletasks()
        x = (history_window.winfo_screenwidth() // 2) - (800 // 2)
        y = (history_window.winfo_screenheight() // 2) - (500 // 2)
        history_window.geometry(f"800x500+{x}+{y}")

        # Title
        tk.Label(
            history_window,
            text="📋 سجل السحوبات - Raffle History 📋",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50',
            bg='#ecf0f1'
        ).pack(pady=10)

        if self.raffle_history:
            # History text area
            history_text = tk.Text(
                history_window,
                font=('Arial', 10),
                bg='#ffffff',
                wrap='word'
            )
            history_text.pack(fill='both', expand=True, padx=20, pady=10)

            history_content = ""
            for i, record in enumerate(self.raffle_history, 1):
                history_content += f"السحب رقم {i} - Raffle #{i}\n"
                history_content += f"التاريخ: {record['date']}\n"
                history_content += f"المشاركون: {record['participants_count']}\n"
                history_content += f"الفائزون: {', '.join(record['winners'])}\n"
                history_content += "=" * 50 + "\n\n"

            history_text.insert('1.0', history_content)
            history_text.config(state='disabled')
        else:
            tk.Label(
                history_window,
                text="لا يوجد سجل سحوبات بعد\nNo raffle history available",
                font=('Arial', 16),
                bg='#ecf0f1',
                fg='#7f8c8d'
            ).pack(expand=True)

        # Buttons
        buttons_frame = tk.Frame(history_window, bg='#ecf0f1')
        buttons_frame.pack(pady=10)

        tk.Button(
            buttons_frame,
            text="مسح السجل\nClear History",
            command=lambda: self.clear_history(history_window),
            bg='#e67e22',
            fg='white',
            font=('Arial', 10),
            width=15
        ).pack(side='left', padx=10)

        tk.Button(
            buttons_frame,
            text="إغلاق\nClose",
            command=history_window.destroy,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 10),
            width=15
        ).pack(side='right', padx=10)

    def clear_history(self, parent_window):
        """Clear raffle history"""
        result = messagebox.askyesno(
            "تأكيد المسح - Confirm Clear",
            "هل أنت متأكد من مسح سجل السحوبات؟\n"
            "Are you sure you want to clear the raffle history?"
        )

        if result:
            self.raffle_history = []
            self.statistics['total_raffles'] = 0
            self.statistics['total_participants'] = 0
            self.statistics['total_winners'] = 0
            self.statistics['last_raffle_date'] = None
            self.play_sound('success_chime', 0.6)
            messagebox.showinfo("تم المسح - Cleared", "تم مسح السجل بنجاح\nHistory cleared successfully")
            parent_window.destroy()

    def create_backup(self):
        """Create backup of all data"""
        try:
            backup_data = {
                'participants': self.participants,
                'winners': self.winners,
                'prize_categories': self.prize_categories,
                'raffle_history': self.raffle_history,
                'statistics': self.statistics,
                'settings': {
                    'sound_enabled': self.sound_enabled,
                    'background_music_enabled': self.background_music_enabled,
                    'animation_speed': int(self.speed_var.get()) if hasattr(self, 'speed_var') else 100
                },
                'backup_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            filename = f"raffle_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialname=filename,
                title="حفظ نسخة احتياطية - Save Backup"
            )

            if filepath:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                self.play_sound('success_chime', 0.6)
                messagebox.showinfo(
                    "تم الحفظ - Saved",
                    f"تم حفظ النسخة الاحتياطية:\n{filepath}"
                )

        except Exception as e:
            self.play_sound('error', 0.6)
            messagebox.showerror(
                "خطأ في الحفظ - Save Error",
                f"حدث خطأ أثناء حفظ النسخة الاحتياطية:\n{str(e)}"
            )

    def restore_backup(self):
        """Restore data from backup file"""
        filepath = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="اختر ملف النسخة الاحتياطية - Select Backup File"
        )

        if not filepath:
            return

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # Confirm restore
            result = messagebox.askyesno(
                "تأكيد الاستعادة - Confirm Restore",
                f"هل أنت متأكد من استعادة البيانات؟\n"
                f"سيتم استبدال جميع البيانات الحالية!\n\n"
                f"تاريخ النسخة الاحتياطية: {backup_data.get('backup_date', 'غير محدد')}\n\n"
                f"Are you sure you want to restore data?\n"
                f"All current data will be replaced!\n\n"
                f"Backup date: {backup_data.get('backup_date', 'Unknown')}"
            )

            if result:
                # Restore data
                self.participants = backup_data.get('participants', [])
                self.winners = backup_data.get('winners', [])
                self.prize_categories = backup_data.get('prize_categories', [])
                self.raffle_history = backup_data.get('raffle_history', [])
                self.statistics = backup_data.get('statistics', {
                    'total_raffles': 0,
                    'total_participants': 0,
                    'total_winners': 0,
                    'last_raffle_date': None
                })

                # Restore settings
                settings = backup_data.get('settings', {})
                self.sound_enabled = settings.get('sound_enabled', True)
                self.background_music_enabled = settings.get('background_music_enabled', False)

                # Update UI
                self.update_participants_count()
                if hasattr(self, 'categories_listbox'):
                    self.update_categories_display()
                if hasattr(self, 'sound_enabled_var'):
                    self.sound_enabled_var.set(self.sound_enabled)
                if hasattr(self, 'background_music_var'):
                    self.background_music_var.set(self.background_music_enabled)

                # Update winners display
                self.winners_display.config(state='normal')
                self.winners_display.delete('1.0', 'end')
                if self.winners:
                    for i, winner in enumerate(self.winners, 1):
                        self.winners_display.insert('end', f"🏆 الفائز {i}: {winner}\n")
                else:
                    self.winners_display.insert('1.0', "الفائزون سيظهرون هنا\nWinners will appear here")
                self.winners_display.config(state='disabled')

                self.play_sound('success_chime', 0.6)
                messagebox.showinfo(
                    "تم الاستعادة - Restored",
                    "تم استعادة البيانات بنجاح\nData restored successfully"
                )

        except Exception as e:
            self.play_sound('error', 0.6)
            messagebox.showerror(
                "خطأ في الاستعادة - Restore Error",
                f"حدث خطأ أثناء استعادة البيانات:\n{str(e)}"
            )

    def generate_certificates(self):
        """Generate winner certificates"""
        if not self.winners:
            self.play_sound('error', 0.6)
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        # Certificate generation dialog
        cert_dialog = tk.Toplevel(self.root)
        cert_dialog.title("إنشاء شهادات الفوز - Generate Winner Certificates")
        cert_dialog.geometry("600x400")
        cert_dialog.configure(bg='#34495e')
        cert_dialog.transient(self.root)
        cert_dialog.grab_set()

        # Center the dialog
        cert_dialog.update_idletasks()
        x = (cert_dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (cert_dialog.winfo_screenheight() // 2) - (400 // 2)
        cert_dialog.geometry(f"600x400+{x}+{y}")

        # Title
        tk.Label(
            cert_dialog,
            text="🏆 إنشاء شهادات الفوز 🏆",
            font=('Arial', 18, 'bold'),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(pady=20)

        # Prize description
        tk.Label(
            cert_dialog,
            text="وصف الجائزة - Prize Description:",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(pady=(10, 5))

        prize_entry = tk.Entry(cert_dialog, font=('Arial', 12), width=50)
        prize_entry.pack(pady=5)
        prize_entry.insert(0, "جائزة السحب الإلكتروني - Electronic Raffle Prize")

        # Winners selection
        tk.Label(
            cert_dialog,
            text="اختر الفائزين - Select Winners:",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(pady=(20, 5))

        # Winners listbox with checkboxes
        winners_frame = tk.Frame(cert_dialog, bg='#34495e')
        winners_frame.pack(pady=10)

        self.cert_winners_vars = []
        for i, winner in enumerate(self.winners):
            var = tk.BooleanVar(value=True)
            self.cert_winners_vars.append(var)

            check = tk.Checkbutton(
                winners_frame,
                text=f"{i+1}. {winner}",
                variable=var,
                font=('Arial', 11),
                fg='#ecf0f1',
                bg='#34495e',
                selectcolor='#2c3e50'
            )
            check.pack(anchor='w', padx=20, pady=2)

        # Buttons
        buttons_frame = tk.Frame(cert_dialog, bg='#34495e')
        buttons_frame.pack(pady=30)

        def generate_selected_certificates():
            prize_desc = prize_entry.get().strip()
            selected_winners = []

            for i, var in enumerate(self.cert_winners_vars):
                if var.get():
                    selected_winners.append(self.winners[i])

            if not selected_winners:
                self.play_sound('error', 0.6)
                messagebox.showwarning(
                    "تحذير - Warning",
                    "يرجى اختيار فائز واحد على الأقل\nPlease select at least one winner"
                )
                return

            # Choose directory to save certificates
            save_dir = filedialog.askdirectory(
                title="اختر مجلد الحفظ - Choose Save Directory"
            )

            if save_dir:
                try:
                    certificate_template = CertificateTemplate()
                    generated_count = 0

                    for winner in selected_winners:
                        filename = os.path.join(save_dir, f"certificate_{winner.replace(' ', '_')}.pdf")
                        certificate_template.generate_certificate(winner, prize_desc, filename)
                        generated_count += 1

                    self.play_sound('celebration', 0.8)
                    messagebox.showinfo(
                        "تم الإنشاء - Generated",
                        f"تم إنشاء {generated_count} شهادة بنجاح\n"
                        f"Generated {generated_count} certificates successfully\n\n"
                        f"المجلد: {save_dir}"
                    )
                    cert_dialog.destroy()

                except Exception as e:
                    self.play_sound('error', 0.6)
                    messagebox.showerror(
                        "خطأ في الإنشاء - Generation Error",
                        f"حدث خطأ أثناء إنشاء الشهادات:\n{str(e)}"
                    )

        tk.Button(
            buttons_frame,
            text="إنشاء الشهادات\nGenerate Certificates",
            command=generate_selected_certificates,
            bg='#2ecc71',
            fg='white',
            font=('Arial', 12),
            width=20
        ).pack(side='left', padx=10)

        tk.Button(
            buttons_frame,
            text="إلغاء\nCancel",
            command=cert_dialog.destroy,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 12),
            width=15
        ).pack(side='right', padx=10)
        
        # Action buttons
        actions_frame = tk.LabelFrame(
            parent,
            text="الإجراءات - Actions",
            font=('Arial', 12, 'bold'),
            fg='#ecf0f1',
            bg='#34495e'
        )
        actions_frame.pack(fill='x', pady=5)
        
        self.start_button = tk.Button(
            actions_frame,
            text="🎲 بدء السحب\nStart Raffle",
            command=lambda: self.button_click_with_sound(self.start_raffle),
            bg='#e74c3c',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='raised',
            bd=3,
            height=2
        )
        self.start_button.pack(fill='x', padx=5, pady=5)
        
        show_results_btn = tk.Button(
            actions_frame,
            text="📊 عرض النتائج\nShow Results",
            command=lambda: self.button_click_with_sound(self.show_results),
            bg='#9b59b6',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        show_results_btn.pack(fill='x', padx=5, pady=2)

        # Export options dropdown
        export_frame = tk.Frame(actions_frame, bg='#34495e')
        export_frame.pack(fill='x', padx=5, pady=2)

        tk.Label(
            export_frame,
            text="تصدير - Export:",
            font=('Arial', 9),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(anchor='w')

        export_buttons_frame = tk.Frame(export_frame, bg='#34495e')
        export_buttons_frame.pack(fill='x', pady=2)

        pdf_basic_btn = tk.Button(
            export_buttons_frame,
            text="PDF أساسي\nBasic PDF",
            command=lambda: self.button_click_with_sound(self.generate_pdf_report),
            bg='#f39c12',
            fg='white',
            font=('Arial', 8),
            relief='raised',
            bd=2,
            width=12
        )
        pdf_basic_btn.pack(side='left', padx=1)

        pdf_detailed_btn = tk.Button(
            export_buttons_frame,
            text="PDF مفصل\nDetailed PDF",
            command=lambda: self.button_click_with_sound(self.generate_detailed_pdf),
            bg='#e67e22',
            fg='white',
            font=('Arial', 8),
            relief='raised',
            bd=2,
            width=12
        )
        pdf_detailed_btn.pack(side='left', padx=1)

        excel_btn = tk.Button(
            export_buttons_frame,
            text="Excel",
            command=lambda: self.button_click_with_sound(self.export_to_excel),
            bg='#27ae60',
            fg='white',
            font=('Arial', 8),
            relief='raised',
            bd=2,
            width=8
        )
        excel_btn.pack(side='right', padx=1)

        # Second row of export buttons
        export_buttons_frame2 = tk.Frame(export_frame, bg='#34495e')
        export_buttons_frame2.pack(fill='x', pady=2)

        json_btn = tk.Button(
            export_buttons_frame2,
            text="JSON",
            command=lambda: self.button_click_with_sound(self.export_to_json),
            bg='#9b59b6',
            fg='white',
            font=('Arial', 8),
            relief='raised',
            bd=2,
            width=8
        )
        json_btn.pack(side='left', padx=1)

        csv_btn = tk.Button(
            export_buttons_frame2,
            text="CSV",
            command=lambda: self.button_click_with_sound(self.export_to_csv),
            bg='#34495e',
            fg='white',
            font=('Arial', 8),
            relief='raised',
            bd=2,
            width=8
        )
        csv_btn.pack(side='left', padx=1)

        all_formats_btn = tk.Button(
            export_buttons_frame2,
            text="جميع الصيغ\nAll Formats",
            command=lambda: self.button_click_with_sound(self.export_all_formats),
            bg='#e74c3c',
            fg='white',
            font=('Arial', 8),
            relief='raised',
            bd=2,
            width=12
        )
        all_formats_btn.pack(side='right', padx=1)

        reset_btn = tk.Button(
            actions_frame,
            text="🔄 إعادة تعيين\nReset",
            command=lambda: self.button_click_with_sound(self.reset_raffle),
            bg='#95a5a6',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        reset_btn.pack(fill='x', padx=5, pady=2)

        # Advanced features buttons
        advanced_frame = tk.LabelFrame(
            parent,
            text="ميزات متقدمة - Advanced Features",
            font=('Arial', 12, 'bold'),
            fg='#ecf0f1',
            bg='#34495e'
        )
        advanced_frame.pack(fill='x', pady=5)

        stats_btn = tk.Button(
            advanced_frame,
            text="📈 الإحصائيات\nStatistics",
            command=lambda: self.button_click_with_sound(self.show_statistics),
            bg='#8e44ad',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        stats_btn.pack(fill='x', padx=5, pady=2)

        history_btn = tk.Button(
            advanced_frame,
            text="📋 سجل السحوبات\nRaffle History",
            command=lambda: self.button_click_with_sound(self.show_raffle_history),
            bg='#16a085',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        history_btn.pack(fill='x', padx=5, pady=2)

        backup_btn = tk.Button(
            advanced_frame,
            text="💾 نسخ احتياطي\nBackup Data",
            command=lambda: self.button_click_with_sound(self.create_backup),
            bg='#d35400',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        backup_btn.pack(fill='x', padx=5, pady=2)

        restore_btn = tk.Button(
            advanced_frame,
            text="📂 استعادة البيانات\nRestore Data",
            command=lambda: self.button_click_with_sound(self.restore_backup),
            bg='#27ae60',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        restore_btn.pack(fill='x', padx=5, pady=2)

        certificates_btn = tk.Button(
            advanced_frame,
            text="🏆 شهادات الفوز\nWinner Certificates",
            command=lambda: self.button_click_with_sound(self.generate_certificates),
            bg='#f39c12',
            fg='white',
            font=('Arial', 10),
            relief='raised',
            bd=2
        )
        certificates_btn.pack(fill='x', padx=5, pady=2)
        
    def setup_right_panel(self, parent):
        """Setup display panel"""
        # Display title
        display_title = tk.Label(
            parent,
            text="🎯 شاشة العرض - Display Screen 🎯",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50',
            bg='#ecf0f1'
        )
        display_title.pack(pady=10)
        
        # Main display area
        self.display_frame = tk.Frame(parent, bg='#ecf0f1')
        self.display_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Current name display
        self.current_name_label = tk.Label(
            self.display_frame,
            text="مرحباً بكم في نظام السحب الإلكتروني\nWelcome to Electronic Raffle System",
            font=('Arial', 20, 'bold'),
            fg='#2c3e50',
            bg='#ecf0f1',
            wraplength=400,
            justify='center'
        )
        self.current_name_label.pack(expand=True)
        
        # Winners display
        self.winners_display = tk.Text(
            self.display_frame,
            height=8,
            font=('Arial', 12),
            bg='#ffffff',
            fg='#2c3e50',
            relief='sunken',
            bd=2,
            wrap='word'
        )
        self.winners_display.pack(fill='x', pady=10)
        self.winners_display.insert('1.0', "الفائزون سيظهرون هنا\nWinners will appear here")
        self.winners_display.config(state='disabled')

    def import_from_excel(self):
        """Import participants from Excel file"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Excel - Select Excel File",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                if file_path.endswith('.csv'):
                    df = pd.read_csv(file_path, encoding='utf-8')
                else:
                    df = pd.read_excel(file_path)

                # Assume first column contains names
                names = df.iloc[:, 0].dropna().tolist()
                self.participants = [str(name).strip() for name in names if str(name).strip()]

                self.update_participants_count()
                self.play_sound('success_chime', 0.6)
                messagebox.showinfo(
                    "نجح الاستيراد - Import Successful",
                    f"تم استيراد {len(self.participants)} مشارك بنجاح\n"
                    f"Successfully imported {len(self.participants)} participants"
                )

            except Exception as e:
                self.play_sound('error', 0.6)
                messagebox.showerror(
                    "خطأ في الاستيراد - Import Error",
                    f"حدث خطأ أثناء استيراد الملف:\n{str(e)}"
                )

    def manual_add_participant(self):
        """Manually add a participant"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة مشارك - Add Participant")
        dialog.geometry("400x200")
        dialog.configure(bg='#34495e')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (200 // 2)
        dialog.geometry(f"400x200+{x}+{y}")

        tk.Label(
            dialog,
            text="اسم المشارك - Participant Name:",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#34495e'
        ).pack(pady=10)

        name_entry = tk.Entry(dialog, font=('Arial', 12), width=30)
        name_entry.pack(pady=5)
        name_entry.focus()

        def add_name():
            name = name_entry.get().strip()
            if name:
                if name not in self.participants:
                    self.participants.append(name)
                    self.update_participants_count()
                    messagebox.showinfo("تم الإضافة - Added", f"تم إضافة: {name}")
                    dialog.destroy()
                else:
                    messagebox.showwarning("تحذير - Warning", "هذا الاسم موجود بالفعل\nThis name already exists")
            else:
                messagebox.showwarning("تحذير - Warning", "يرجى إدخال اسم صحيح\nPlease enter a valid name")

        tk.Button(
            dialog,
            text="إضافة - Add",
            command=add_name,
            bg='#2ecc71',
            fg='white',
            font=('Arial', 12),
            width=15
        ).pack(pady=10)

        # Bind Enter key to add name
        name_entry.bind('<Return>', lambda e: add_name())

    def update_participants_count(self):
        """Update participants count display"""
        count = len(self.participants)
        self.participants_count_label.config(
            text=f"عدد المشاركين: {count}\nParticipants: {count}"
        )

    def start_raffle(self):
        """Start the raffle animation and selection"""
        if not self.participants:
            self.play_sound('error', 0.6)
            messagebox.showwarning(
                "تحذير - Warning",
                "لا يوجد مشاركون!\nNo participants found!"
            )
            return

        try:
            winners_count = int(self.winners_count_var.get())
        except ValueError:
            self.play_sound('error', 0.6)
            messagebox.showerror("خطأ - Error", "يرجى إدخال عدد صحيح للفائزين\nPlease enter a valid number of winners")
            return

        if winners_count > len(self.participants) and not self.repeat_winners_var.get():
            self.play_sound('error', 0.6)
            messagebox.showwarning(
                "تحذير - Warning",
                "عدد الفائزين أكبر من عدد المشاركين!\nNumber of winners exceeds participants!"
            )
            return

        # Play raffle start sound
        self.play_sound('raffle_start', 0.8)

        self.current_animation = True
        self.start_button.config(state='disabled', text="جاري السحب...\nDrawing...")

        # Start animation in separate thread
        animation_thread = threading.Thread(target=self.animate_raffle, args=(winners_count,))
        animation_thread.daemon = True
        animation_thread.start()

    def animate_raffle(self, winners_count):
        """Animate the raffle selection"""
        self.animation_speed = int(self.speed_var.get())

        # Clear previous winners display
        self.winners_display.config(state='normal')
        self.winners_display.delete('1.0', 'end')
        self.winners_display.config(state='disabled')

        selected_winners = []
        available_participants = self.participants.copy()

        # Play drum roll sound at the beginning
        self.play_sound('drum_roll', 0.6)

        for winner_num in range(winners_count):
            if not available_participants and not self.repeat_winners_var.get():
                break

            # Play rolling sound for this winner
            self.play_sound('rolling', 0.4)

            # Animation phase
            for i in range(30):  # 30 animation cycles
                if not self.current_animation:
                    break

                random_name = random.choice(self.participants)
                self.current_name_label.config(
                    text=f"🎲 {random_name} 🎲",
                    font=('Arial', 24, 'bold'),
                    fg='#e74c3c'
                )
                self.root.update()
                time.sleep(self.animation_speed / 1000.0)

            # Select winner
            if self.repeat_winners_var.get():
                winner = random.choice(self.participants)
            else:
                winner = random.choice(available_participants)
                available_participants.remove(winner)

            selected_winners.append(winner)

            # Play winner sound
            self.play_sound('winner', 0.8)

            # Display winner
            self.current_name_label.config(
                text=f"🏆 الفائز {winner_num + 1}: {winner} 🏆\nWinner {winner_num + 1}: {winner}",
                font=('Arial', 20, 'bold'),
                fg='#27ae60'
            )

            # Update winners display
            self.winners_display.config(state='normal')
            self.winners_display.insert('end', f"🏆 الفائز {winner_num + 1}: {winner}\n")
            self.winners_display.config(state='disabled')
            self.winners_display.see('end')

            self.root.update()
            time.sleep(1)  # Pause between winners

        self.winners = selected_winners
        self.current_animation = False
        self.start_button.config(state='normal', text="🎲 بدء السحب\nStart Raffle")

        # Update statistics
        self.update_statistics(selected_winners)

        # Save to history
        self.save_to_history(selected_winners)

        # Play celebration sound
        self.play_sound('celebration', 0.9)

        # Show completion message
        self.play_sound('success_chime', 0.7)
        messagebox.showinfo(
            "انتهى السحب - Raffle Complete",
            f"تم اختيار {len(selected_winners)} فائز\n"
            f"Selected {len(selected_winners)} winners"
        )

    def update_statistics(self, winners):
        """Update system statistics"""
        self.statistics['total_raffles'] += 1
        self.statistics['total_participants'] += len(self.participants)
        self.statistics['total_winners'] += len(winners)
        self.statistics['last_raffle_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def save_to_history(self, winners):
        """Save raffle to history"""
        history_record = {
            'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'participants_count': len(self.participants),
            'winners': winners.copy(),
            'prize_categories_used': len(self.prize_categories),
            'settings': {
                'allow_repeat': self.repeat_winners_var.get() if hasattr(self, 'repeat_winners_var') else False,
                'animation_speed': int(self.speed_var.get()) if hasattr(self, 'speed_var') else 100
            }
        }
        self.raffle_history.append(history_record)

    def show_results(self):
        """Show detailed results window"""
        if not self.winners:
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        results_window = tk.Toplevel(self.root)
        results_window.title("نتائج السحب - Raffle Results")
        results_window.geometry("600x500")
        results_window.configure(bg='#ecf0f1')
        results_window.transient(self.root)

        # Center the window
        results_window.update_idletasks()
        x = (results_window.winfo_screenwidth() // 2) - (600 // 2)
        y = (results_window.winfo_screenheight() // 2) - (500 // 2)
        results_window.geometry(f"600x500+{x}+{y}")

        # Title
        tk.Label(
            results_window,
            text="🏆 نتائج السحب - Raffle Results 🏆",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50',
            bg='#ecf0f1'
        ).pack(pady=10)

        # Results text area
        results_text = tk.Text(
            results_window,
            font=('Arial', 12),
            bg='#ffffff',
            fg='#2c3e50',
            relief='sunken',
            bd=2,
            wrap='word'
        )
        results_text.pack(fill='both', expand=True, padx=20, pady=10)

        # Add results content
        results_content = f"تاريخ السحب - Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        results_content += f"عدد المشاركين - Total Participants: {len(self.participants)}\n"
        results_content += f"عدد الفائزين - Number of Winners: {len(self.winners)}\n"
        results_content += "=" * 50 + "\n\n"

        for i, winner in enumerate(self.winners, 1):
            results_content += f"🏆 الفائز {i} - Winner {i}: {winner}\n"

        results_content += "\n" + "=" * 50 + "\n"
        results_content += "جميع المشاركين - All Participants:\n"
        for i, participant in enumerate(self.participants, 1):
            results_content += f"{i}. {participant}\n"

        results_text.insert('1.0', results_content)
        results_text.config(state='disabled')

        # Buttons frame
        buttons_frame = tk.Frame(results_window, bg='#ecf0f1')
        buttons_frame.pack(pady=10)

        tk.Button(
            buttons_frame,
            text="حفظ كـ Excel\nSave as Excel",
            command=self.export_to_excel,
            bg='#27ae60',
            fg='white',
            font=('Arial', 10),
            width=15
        ).pack(side='left', padx=5)

        tk.Button(
            buttons_frame,
            text="إغلاق\nClose",
            command=results_window.destroy,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 10),
            width=15
        ).pack(side='right', padx=5)

    def generate_pdf_report(self):
        """Generate PDF report of raffle results"""
        if not self.winners:
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        try:
            filename = f"raffle_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                initialname=filename,
                title="حفظ تقرير PDF - Save PDF Report"
            )

            if not filepath:
                return

            # Create PDF
            c = canvas.Canvas(filepath, pagesize=A4)
            width, height = A4

            # Title
            c.setFont("Helvetica-Bold", 20)
            c.drawCentredText(width/2, height-50, "Electronic Raffle System Results")
            c.drawCentredText(width/2, height-75, "نتائج نظام السحب الإلكتروني")

            # Date and info
            c.setFont("Helvetica", 12)
            y_position = height - 120
            c.drawString(50, y_position, f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            y_position -= 20
            c.drawString(50, y_position, f"Total Participants: {len(self.participants)}")
            y_position -= 20
            c.drawString(50, y_position, f"Number of Winners: {len(self.winners)}")
            y_position -= 40

            # Winners section
            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, y_position, "Winners - الفائزون:")
            y_position -= 25

            c.setFont("Helvetica", 12)
            for i, winner in enumerate(self.winners, 1):
                if y_position < 100:  # Start new page if needed
                    c.showPage()
                    y_position = height - 50
                c.drawString(70, y_position, f"{i}. {winner}")
                y_position -= 20

            y_position -= 20

            # Participants section
            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, y_position, "All Participants - جميع المشاركين:")
            y_position -= 25

            c.setFont("Helvetica", 10)
            for i, participant in enumerate(self.participants, 1):
                if y_position < 100:  # Start new page if needed
                    c.showPage()
                    y_position = height - 50
                c.drawString(70, y_position, f"{i}. {participant}")
                y_position -= 15

            c.save()

            messagebox.showinfo(
                "تم الحفظ - Saved Successfully",
                f"تم حفظ التقرير بنجاح:\n{filepath}"
            )

        except Exception as e:
            messagebox.showerror(
                "خطأ في الحفظ - Save Error",
                f"حدث خطأ أثناء حفظ التقرير:\n{str(e)}"
            )

    def generate_detailed_pdf(self):
        """Generate detailed PDF report using templates"""
        if not self.winners:
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        try:
            filename = f"detailed_raffle_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                initialname=filename,
                title="حفظ التقرير المفصل - Save Detailed Report"
            )

            if not filepath:
                return

            # Prepare data for template
            report_data = {
                'participants': self.participants,
                'winners': self.winners,
                'prize_categories': self.prize_categories,
                'statistics': self.statistics,
                'raffle_history': self.raffle_history
            }

            # Generate detailed report
            detailed_template = DetailedReportTemplate()
            detailed_template.generate_report(report_data, filepath)

            self.play_sound('success_chime', 0.6)
            messagebox.showinfo(
                "تم الحفظ - Saved Successfully",
                f"تم حفظ التقرير المفصل بنجاح:\n{filepath}"
            )

        except Exception as e:
            self.play_sound('error', 0.6)
            messagebox.showerror(
                "خطأ في الحفظ - Save Error",
                f"حدث خطأ أثناء حفظ التقرير المفصل:\n{str(e)}"
            )

    def export_to_json(self):
        """Export all data to JSON format"""
        try:
            filename = f"raffle_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialname=filename,
                title="حفظ ملف JSON - Save JSON File"
            )

            if not filepath:
                return

            # Prepare comprehensive data
            export_data = {
                'raffle_info': {
                    'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'total_participants': len(self.participants),
                    'total_winners': len(self.winners),
                    'prize_categories_count': len(self.prize_categories)
                },
                'participants': self.participants,
                'winners': self.winners,
                'prize_categories': self.prize_categories,
                'statistics': self.statistics,
                'raffle_history': self.raffle_history,
                'settings': {
                    'allow_repeat_winners': self.repeat_winners_var.get() if hasattr(self, 'repeat_winners_var') else False,
                    'animation_speed': int(self.speed_var.get()) if hasattr(self, 'speed_var') else 100,
                    'sound_enabled': self.sound_enabled,
                    'background_music_enabled': self.background_music_enabled
                }
            }

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.play_sound('success_chime', 0.6)
            messagebox.showinfo(
                "تم الحفظ - Saved Successfully",
                f"تم حفظ ملف JSON بنجاح:\n{filepath}"
            )

        except Exception as e:
            self.play_sound('error', 0.6)
            messagebox.showerror(
                "خطأ في الحفظ - Save Error",
                f"حدث خطأ أثناء حفظ ملف JSON:\n{str(e)}"
            )

    def export_to_csv(self):
        """Export results to CSV format"""
        if not self.winners:
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        try:
            filename = f"raffle_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            filepath = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialname=filename,
                title="حفظ ملف CSV - Save CSV File"
            )

            if not filepath:
                return

            # Create CSV data
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # Header
                writer.writerow(['Rank', 'Winner Name', 'Draw Date', 'Total Participants'])

                # Winners data
                draw_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                for i, winner in enumerate(self.winners, 1):
                    writer.writerow([i, winner, draw_date, len(self.participants)])

                # Add separator
                writer.writerow([])
                writer.writerow(['All Participants:'])

                # All participants
                for i, participant in enumerate(self.participants, 1):
                    writer.writerow([i, participant])

            self.play_sound('success_chime', 0.6)
            messagebox.showinfo(
                "تم الحفظ - Saved Successfully",
                f"تم حفظ ملف CSV بنجاح:\n{filepath}"
            )

        except Exception as e:
            self.play_sound('error', 0.6)
            messagebox.showerror(
                "خطأ في الحفظ - Save Error",
                f"حدث خطأ أثناء حفظ ملف CSV:\n{str(e)}"
            )

    def export_all_formats(self):
        """Export results in all available formats"""
        if not self.winners:
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        # Choose directory
        save_dir = filedialog.askdirectory(
            title="اختر مجلد الحفظ - Choose Save Directory"
        )

        if not save_dir:
            return

        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_filename = f"raffle_export_{timestamp}"

            exported_files = []

            # Export basic PDF
            try:
                pdf_basic_path = os.path.join(save_dir, f"{base_filename}_basic.pdf")
                self.generate_pdf_report_to_file(pdf_basic_path)
                exported_files.append("Basic PDF")
            except Exception as e:
                print(f"Error exporting basic PDF: {e}")

            # Export detailed PDF
            try:
                pdf_detailed_path = os.path.join(save_dir, f"{base_filename}_detailed.pdf")
                report_data = {
                    'participants': self.participants,
                    'winners': self.winners,
                    'prize_categories': self.prize_categories,
                    'statistics': self.statistics,
                    'raffle_history': self.raffle_history
                }
                detailed_template = DetailedReportTemplate()
                detailed_template.generate_report(report_data, pdf_detailed_path)
                exported_files.append("Detailed PDF")
            except Exception as e:
                print(f"Error exporting detailed PDF: {e}")

            # Export Excel
            try:
                excel_path = os.path.join(save_dir, f"{base_filename}.xlsx")
                self.export_to_excel_file(excel_path)
                exported_files.append("Excel")
            except Exception as e:
                print(f"Error exporting Excel: {e}")

            # Export JSON
            try:
                json_path = os.path.join(save_dir, f"{base_filename}.json")
                export_data = {
                    'raffle_info': {
                        'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'total_participants': len(self.participants),
                        'total_winners': len(self.winners),
                        'prize_categories_count': len(self.prize_categories)
                    },
                    'participants': self.participants,
                    'winners': self.winners,
                    'prize_categories': self.prize_categories,
                    'statistics': self.statistics,
                    'raffle_history': self.raffle_history
                }
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
                exported_files.append("JSON")
            except Exception as e:
                print(f"Error exporting JSON: {e}")

            # Export CSV
            try:
                csv_path = os.path.join(save_dir, f"{base_filename}.csv")
                with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['Rank', 'Winner Name', 'Draw Date', 'Total Participants'])
                    draw_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    for i, winner in enumerate(self.winners, 1):
                        writer.writerow([i, winner, draw_date, len(self.participants)])
                exported_files.append("CSV")
            except Exception as e:
                print(f"Error exporting CSV: {e}")

            if exported_files:
                self.play_sound('celebration', 0.8)
                messagebox.showinfo(
                    "تم التصدير - Export Complete",
                    f"تم تصدير الملفات بنجاح:\n"
                    f"Successfully exported files:\n\n"
                    f"• {', '.join(exported_files)}\n\n"
                    f"المجلد: {save_dir}"
                )
            else:
                self.play_sound('error', 0.6)
                messagebox.showerror(
                    "خطأ في التصدير - Export Error",
                    "فشل في تصدير جميع الملفات\nFailed to export all files"
                )

        except Exception as e:
            self.play_sound('error', 0.6)
            messagebox.showerror(
                "خطأ في التصدير - Export Error",
                f"حدث خطأ أثناء التصدير:\n{str(e)}"
            )

    def generate_pdf_report_to_file(self, filepath):
        """Generate basic PDF report to specific file"""
        # Create PDF
        c = canvas.Canvas(filepath, pagesize=A4)
        width, height = A4

        # Title
        c.setFont("Helvetica-Bold", 20)
        c.drawCentredString(width/2, height-50, "Electronic Raffle System Results")
        c.drawCentredString(width/2, height-75, "نتائج نظام السحب الإلكتروني")

        # Date and info
        c.setFont("Helvetica", 12)
        y_position = height - 120
        c.drawString(50, y_position, f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        y_position -= 20
        c.drawString(50, y_position, f"Total Participants: {len(self.participants)}")
        y_position -= 20
        c.drawString(50, y_position, f"Number of Winners: {len(self.winners)}")
        y_position -= 40

        # Winners section
        c.setFont("Helvetica-Bold", 14)
        c.drawString(50, y_position, "Winners - الفائزون:")
        y_position -= 25

        c.setFont("Helvetica", 12)
        for i, winner in enumerate(self.winners, 1):
            if y_position < 100:  # Start new page if needed
                c.showPage()
                y_position = height - 50
            c.drawString(70, y_position, f"{i}. {winner}")
            y_position -= 20

        c.save()

    def export_to_excel_file(self, filepath):
        """Export to Excel file at specific path"""
        # Create DataFrame with results
        data = {
            'Winner Position': [f"Winner {i+1}" for i in range(len(self.winners))],
            'Winner Name': self.winners,
            'Draw Date': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')] * len(self.winners)
        }

        df_winners = pd.DataFrame(data)

        # Create DataFrame with all participants
        df_participants = pd.DataFrame({
            'Participant Number': range(1, len(self.participants) + 1),
            'Participant Name': self.participants
        })

        # Create statistics DataFrame
        df_statistics = pd.DataFrame({
            'Statistic': ['Total Raffles', 'Total Participants', 'Total Winners', 'Last Raffle Date'],
            'Value': [
                self.statistics['total_raffles'],
                self.statistics['total_participants'],
                self.statistics['total_winners'],
                self.statistics['last_raffle_date'] or 'N/A'
            ]
        })

        # Save to Excel with multiple sheets
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df_winners.to_excel(writer, sheet_name='Winners', index=False)
            df_participants.to_excel(writer, sheet_name='All Participants', index=False)
            df_statistics.to_excel(writer, sheet_name='Statistics', index=False)

            if self.prize_categories:
                df_categories = pd.DataFrame(self.prize_categories)
                df_categories.to_excel(writer, sheet_name='Prize Categories', index=False)

    def export_to_excel(self):
        """Export results to Excel file"""
        if not self.winners:
            messagebox.showinfo(
                "لا توجد نتائج - No Results",
                "لم يتم إجراء أي سحب بعد\nNo raffle has been conducted yet"
            )
            return

        try:
            filename = f"raffle_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            filepath = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                initialname=filename,
                title="حفظ ملف Excel - Save Excel File"
            )

            if not filepath:
                return

            # Create DataFrame with results
            data = {
                'Winner Position': [f"Winner {i+1}" for i in range(len(self.winners))],
                'Winner Name': self.winners,
                'Draw Date': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')] * len(self.winners)
            }

            df_winners = pd.DataFrame(data)

            # Create DataFrame with all participants
            df_participants = pd.DataFrame({
                'Participant Number': range(1, len(self.participants) + 1),
                'Participant Name': self.participants
            })

            # Save to Excel with multiple sheets
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df_winners.to_excel(writer, sheet_name='Winners', index=False)
                df_participants.to_excel(writer, sheet_name='All Participants', index=False)

            messagebox.showinfo(
                "تم الحفظ - Saved Successfully",
                f"تم حفظ الملف بنجاح:\n{filepath}"
            )

        except Exception as e:
            messagebox.showerror(
                "خطأ في الحفظ - Save Error",
                f"حدث خطأ أثناء حفظ الملف:\n{str(e)}"
            )

    def reset_raffle(self):
        """Reset the raffle system"""
        result = messagebox.askyesno(
            "تأكيد إعادة التعيين - Confirm Reset",
            "هل أنت متأكد من إعادة تعيين النظام؟\nسيتم حذف جميع البيانات!\n\n"
            "Are you sure you want to reset the system?\nAll data will be deleted!"
        )

        if result:
            self.participants = []
            self.winners = []
            self.current_animation = False

            # Reset UI
            self.update_participants_count()
            self.current_name_label.config(
                text="مرحباً بكم في نظام السحب الإلكتروني\nWelcome to Electronic Raffle System",
                font=('Arial', 20, 'bold'),
                fg='#2c3e50'
            )

            self.winners_display.config(state='normal')
            self.winners_display.delete('1.0', 'end')
            self.winners_display.insert('1.0', "الفائزون سيظهرون هنا\nWinners will appear here")
            self.winners_display.config(state='disabled')

            self.start_button.config(state='normal', text="🎲 بدء السحب\nStart Raffle")

            messagebox.showinfo(
                "تم إعادة التعيين - Reset Complete",
                "تم إعادة تعيين النظام بنجاح\nSystem reset successfully"
            )

    def load_settings(self):
        """Load settings from file"""
        try:
            if os.path.exists('raffle_settings.json'):
                with open('raffle_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.winners_count_var.set(str(settings.get('winners_count', 1)))
                    self.repeat_winners_var.set(settings.get('allow_repeat', False))
                    self.speed_var.set(str(settings.get('animation_speed', 100)))
                    self.sound_enabled = settings.get('sound_enabled', True)
                    self.background_music_enabled = settings.get('background_music_enabled', False)

                    # Load advanced data
                    self.prize_categories = settings.get('prize_categories', [])
                    self.raffle_history = settings.get('raffle_history', [])
                    self.statistics = settings.get('statistics', {
                        'total_raffles': 0,
                        'total_participants': 0,
                        'total_winners': 0,
                        'last_raffle_date': None
                    })

                    # Update UI variables if they exist
                    if hasattr(self, 'sound_enabled_var'):
                        self.sound_enabled_var.set(self.sound_enabled)
                    if hasattr(self, 'background_music_var'):
                        self.background_music_var.set(self.background_music_enabled)
                    if hasattr(self, 'categories_listbox'):
                        self.update_categories_display()
        except Exception:
            pass  # Use default settings if loading fails

    def save_settings(self):
        """Save settings to file"""
        try:
            settings = {
                'winners_count': int(self.winners_count_var.get()) if hasattr(self, 'winners_count_var') else 1,
                'allow_repeat': self.repeat_winners_var.get() if hasattr(self, 'repeat_winners_var') else False,
                'animation_speed': int(self.speed_var.get()) if hasattr(self, 'speed_var') else 100,
                'sound_enabled': self.sound_enabled,
                'background_music_enabled': self.background_music_enabled,
                'prize_categories': self.prize_categories,
                'raffle_history': self.raffle_history,
                'statistics': self.statistics
            }
            with open('raffle_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception:
            pass  # Ignore save errors

    def on_closing(self):
        """Handle window closing"""
        self.save_settings()
        if self.current_animation:
            self.current_animation = False
        self.root.destroy()

    def run(self):
        """Run the application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """Main function to run the Electronic Raffle System"""
    try:
        app = ElectronicRaffleSystem()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("خطأ في التطبيق - Application Error", f"حدث خطأ في بدء التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
