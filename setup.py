#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup script for Electronic Raffle System
نص إعداد نظام السحب الإلكتروني
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("🔧 تثبيت المكتبات المطلوبة...")
    print("🔧 Installing required packages...")
    
    requirements = [
        'pandas>=1.3.0',
        'openpyxl>=3.0.0',
        'reportlab>=3.6.0'
    ]
    
    for requirement in requirements:
        try:
            print(f"Installing {requirement}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', requirement])
            print(f"✅ {requirement} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Error installing {requirement}: {e}")
            return False
    
    return True

def create_sample_data():
    """Create sample data files"""
    print("\n📊 إنشاء البيانات التجريبية...")
    print("📊 Creating sample data...")
    
    try:
        subprocess.check_call([sys.executable, 'create_sample_data.py'])
        print("✅ Sample data created successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error creating sample data: {e}")
        return False

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if sys.platform == "win32":
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "Electronic Raffle System.lnk")
            target = os.path.join(os.getcwd(), "electronic_raffle_system.py")
            wDir = os.getcwd()
            icon = target
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = icon
            shortcut.save()
            
            print("✅ Desktop shortcut created")
            return True
        except ImportError:
            print("⚠️ Could not create desktop shortcut (winshell not available)")
            return False
        except Exception as e:
            print(f"⚠️ Could not create desktop shortcut: {e}")
            return False
    else:
        print("ℹ️ Desktop shortcut creation is only supported on Windows")
        return True

def main():
    """Main setup function"""
    print("=" * 60)
    print("🎉 مرحباً بكم في إعداد نظام السحب الإلكتروني")
    print("🎉 Welcome to Electronic Raffle System Setup")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print("❌ يتطلب Python 3.7 أو أحدث")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        print("❌ فشل في تثبيت المتطلبات")
        sys.exit(1)
    
    # Create sample data
    if not create_sample_data():
        print("⚠️ Warning: Could not create sample data")
        print("⚠️ تحذير: لم يتم إنشاء البيانات التجريبية")
    
    # Create desktop shortcut
    create_desktop_shortcut()
    
    print("\n" + "=" * 60)
    print("🎉 تم الإعداد بنجاح!")
    print("🎉 Setup completed successfully!")
    print("=" * 60)
    
    print("\n📋 للبدء:")
    print("📋 To start:")
    print("   python electronic_raffle_system.py")
    
    print("\n📚 للمساعدة:")
    print("📚 For help:")
    print("   اقرأ ملف README.md")
    print("   Read the README.md file")
    
    print("\n🎯 ملفات تجريبية:")
    print("🎯 Sample files:")
    print("   - sample_participants.xlsx")
    print("   - sample_participants.csv")
    
    input("\nاضغط Enter للمتابعة... Press Enter to continue...")

if __name__ == "__main__":
    main()
