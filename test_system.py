#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Electronic Raffle System
سكريبت اختبار نظام السحب الإلكتروني
"""

import sys
import os
import importlib.util

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 اختبار المكتبات المطلوبة...")
    print("🔍 Testing required modules...")
    
    modules = {
        'tkinter': 'GUI framework',
        'pandas': 'Data manipulation',
        'openpyxl': 'Excel file handling',
        'reportlab': 'PDF generation',
        'pygame': 'Sound system',
        'numpy': 'Sound generation',
        'json': 'Settings storage',
        'csv': 'CSV file handling',
        'random': 'Random number generation',
        'threading': 'Multi-threading',
        'datetime': 'Date and time'
    }
    
    failed_modules = []
    
    for module, description in modules.items():
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError as e:
            print(f"❌ {module} - {description} - Error: {e}")
            failed_modules.append(module)
    
    return len(failed_modules) == 0

def test_file_structure():
    """Test if all required files exist"""
    print("\n📁 اختبار هيكل الملفات...")
    print("📁 Testing file structure...")
    
    required_files = [
        'electronic_raffle_system.py',
        'requirements.txt',
        'create_sample_data.py',
        'create_sound_effects.py',
        'setup.py',
        'README.md',
        'USER_MANUAL.md'
    ]
    
    optional_files = [
        'sample_participants.xlsx',
        'sample_participants.csv',
        'run_raffle_system.bat',
        'sounds/'  # Sound effects directory
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - Missing!")
            missing_files.append(file)
    
    print("\nOptional files:")
    for file in optional_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file} - Not found (optional)")
    
    return len(missing_files) == 0

def test_main_application():
    """Test if the main application can be imported"""
    print("\n🎯 اختبار التطبيق الرئيسي...")
    print("🎯 Testing main application...")
    
    try:
        spec = importlib.util.spec_from_file_location("electronic_raffle_system", "electronic_raffle_system.py")
        module = importlib.util.module_from_spec(spec)
        
        # Test import without running GUI
        print("✅ Main application file can be imported")
        return True
        
    except Exception as e:
        print(f"❌ Error importing main application: {e}")
        return False

def test_sample_data():
    """Test sample data creation"""
    print("\n📊 اختبار البيانات التجريبية...")
    print("📊 Testing sample data...")
    
    try:
        import pandas as pd
        
        # Test Excel file
        if os.path.exists('sample_participants.xlsx'):
            df = pd.read_excel('sample_participants.xlsx')
            if len(df) > 0:
                print(f"✅ Excel sample file: {len(df)} participants")
            else:
                print("⚠️ Excel sample file is empty")
        
        # Test CSV file
        if os.path.exists('sample_participants.csv'):
            df = pd.read_csv('sample_participants.csv')
            if len(df) > 0:
                print(f"✅ CSV sample file: {len(df)} participants")
            else:
                print("⚠️ CSV sample file is empty")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing sample data: {e}")
        return False

def test_sound_system():
    """Test sound system and effects"""
    print("\n🔊 اختبار نظام الصوت...")
    print("🔊 Testing sound system...")

    try:
        # Check if sounds directory exists
        if not os.path.exists('sounds'):
            print("⚠️ Sounds directory not found - creating sound effects...")
            import subprocess
            result = subprocess.run(['python', 'create_sound_effects.py'],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Sound effects created successfully")
            else:
                print(f"❌ Error creating sound effects: {result.stderr}")
                return False

        # Check sound files
        expected_sounds = [
            'button_click.wav',
            'raffle_start.wav',
            'rolling.wav',
            'winner.wav',
            'celebration.wav',
            'drum_roll.wav',
            'success_chime.wav',
            'error.wav',
            'background_ambient.wav'
        ]

        missing_sounds = []
        for sound_file in expected_sounds:
            sound_path = os.path.join('sounds', sound_file)
            if os.path.exists(sound_path):
                print(f"✅ {sound_file}")
            else:
                print(f"❌ {sound_file} - Missing!")
                missing_sounds.append(sound_file)

        if missing_sounds:
            print(f"⚠️ Missing {len(missing_sounds)} sound files")
            return False

        # Test pygame import
        import pygame
        pygame.mixer.init()
        print("✅ Pygame sound system initialized")
        pygame.mixer.quit()

        return True

    except Exception as e:
        print(f"❌ Error testing sound system: {e}")
        return False

def run_comprehensive_test():
    """Run all tests"""
    print("=" * 60)
    print("🧪 اختبار شامل لنظام السحب الإلكتروني")
    print("🧪 Comprehensive Electronic Raffle System Test")
    print("=" * 60)
    
    print(f"🐍 Python version: {sys.version}")
    print(f"💻 Platform: {sys.platform}")
    print(f"📂 Working directory: {os.getcwd()}")
    
    tests = [
        ("Module Imports", test_imports),
        ("File Structure", test_file_structure),
        ("Main Application", test_main_application),
        ("Sample Data", test_sample_data),
        ("Sound System", test_sound_system)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار - Test Results")
    print("=" * 60)
    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"❌ Failed: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("🎉 All tests passed! System is ready to use")
        print("\n📋 للبدء:")
        print("📋 To start:")
        print("   python electronic_raffle_system.py")
        print("   أو / or")
        print("   run_raffle_system.bat (Windows)")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        print("⚠️ Some tests failed. Please review the errors above")
        print("\n🔧 للإصلاح:")
        print("🔧 To fix:")
        print("   python setup.py")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_comprehensive_test()
    input("\nاضغط Enter للخروج... Press Enter to exit...")
    sys.exit(0 if success else 1)
