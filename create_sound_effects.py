#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sound Effects Generator for Electronic Raffle System
مولد المؤثرات الصوتية لنظام السحب الإلكتروني
"""

import numpy as np
import wave
import os
import struct
import math

class SoundGenerator:
    def __init__(self, sample_rate=44100):
        self.sample_rate = sample_rate
        
    def generate_tone(self, frequency, duration, amplitude=0.5):
        """Generate a pure tone"""
        frames = int(duration * self.sample_rate)
        wave_data = []
        
        for i in range(frames):
            value = amplitude * math.sin(2 * math.pi * frequency * i / self.sample_rate)
            wave_data.append(int(value * 32767))
        
        return wave_data
    
    def generate_chord(self, frequencies, duration, amplitude=0.3):
        """Generate a chord with multiple frequencies"""
        frames = int(duration * self.sample_rate)
        wave_data = []
        
        for i in range(frames):
            value = 0
            for freq in frequencies:
                value += amplitude * math.sin(2 * math.pi * freq * i / self.sample_rate)
            value /= len(frequencies)  # Normalize
            wave_data.append(int(value * 32767))
        
        return wave_data
    
    def generate_sweep(self, start_freq, end_freq, duration, amplitude=0.5):
        """Generate a frequency sweep"""
        frames = int(duration * self.sample_rate)
        wave_data = []
        
        for i in range(frames):
            # Linear frequency sweep
            progress = i / frames
            current_freq = start_freq + (end_freq - start_freq) * progress
            value = amplitude * math.sin(2 * math.pi * current_freq * i / self.sample_rate)
            wave_data.append(int(value * 32767))
        
        return wave_data
    
    def generate_drum_beat(self, duration=0.1, amplitude=0.7):
        """Generate a drum-like beat"""
        frames = int(duration * self.sample_rate)
        wave_data = []
        
        for i in range(frames):
            # Exponential decay envelope
            envelope = math.exp(-i / (frames * 0.3))
            # Mix of low frequencies for drum sound
            value = envelope * amplitude * (
                0.5 * math.sin(2 * math.pi * 60 * i / self.sample_rate) +
                0.3 * math.sin(2 * math.pi * 120 * i / self.sample_rate) +
                0.2 * np.random.uniform(-1, 1)  # Add some noise
            )
            wave_data.append(int(value * 32767))
        
        return wave_data
    
    def save_wave_file(self, wave_data, filename):
        """Save wave data to a WAV file"""
        with wave.open(filename, 'w') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(self.sample_rate)
            
            # Convert to bytes
            wave_bytes = b''.join(struct.pack('<h', sample) for sample in wave_data)
            wav_file.writeframes(wave_bytes)

def create_sound_effects():
    """Create all sound effects for the raffle system"""
    print("🎵 إنشاء المؤثرات الصوتية...")
    print("🎵 Creating sound effects...")
    
    # Create sounds directory
    if not os.path.exists('sounds'):
        os.makedirs('sounds')
        print("📁 Created sounds directory")
    
    generator = SoundGenerator()
    
    # 1. Button click sound - نقرة زر
    print("🔘 Creating button click sound...")
    click_data = generator.generate_tone(800, 0.1, 0.3)
    generator.save_wave_file(click_data, 'sounds/button_click.wav')
    
    # 2. Raffle start sound - بداية السحب
    print("🎲 Creating raffle start sound...")
    start_data = generator.generate_sweep(200, 800, 1.0, 0.4)
    generator.save_wave_file(start_data, 'sounds/raffle_start.wav')
    
    # 3. Rolling/spinning sound - صوت الدوران
    print("🌀 Creating rolling sound...")
    rolling_data = []
    for i in range(20):  # 2 seconds of rolling
        freq = 300 + (i % 4) * 50  # Varying frequency
        segment = generator.generate_tone(freq, 0.1, 0.2)
        rolling_data.extend(segment)
    generator.save_wave_file(rolling_data, 'sounds/rolling.wav')
    
    # 4. Winner announcement - إعلان الفائز
    print("🏆 Creating winner sound...")
    winner_chord = [523, 659, 784]  # C major chord
    winner_data = generator.generate_chord(winner_chord, 2.0, 0.5)
    generator.save_wave_file(winner_data, 'sounds/winner.wav')
    
    # 5. Celebration fanfare - احتفال
    print("🎉 Creating celebration sound...")
    celebration_data = []
    # Ascending notes
    notes = [523, 587, 659, 698, 784, 880, 988, 1047]  # C major scale
    for note in notes:
        segment = generator.generate_tone(note, 0.3, 0.4)
        celebration_data.extend(segment)
    generator.save_wave_file(celebration_data, 'sounds/celebration.wav')
    
    # 6. Drum roll - طبلة
    print("🥁 Creating drum roll...")
    drum_data = []
    for i in range(30):  # 3 seconds of drum roll
        beat = generator.generate_drum_beat(0.1, 0.3)
        drum_data.extend(beat)
    generator.save_wave_file(drum_data, 'sounds/drum_roll.wav')
    
    # 7. Success chime - نغمة النجاح
    print("✨ Creating success chime...")
    chime_notes = [1047, 1319, 1568]  # High C major chord
    chime_data = generator.generate_chord(chime_notes, 1.5, 0.4)
    generator.save_wave_file(chime_data, 'sounds/success_chime.wav')
    
    # 8. Error sound - صوت خطأ
    print("❌ Creating error sound...")
    error_data = generator.generate_tone(200, 0.5, 0.4)
    generator.save_wave_file(error_data, 'sounds/error.wav')
    
    # 9. Background ambient - خلفية هادئة
    print("🎼 Creating background ambient...")
    ambient_data = []
    for i in range(100):  # 10 seconds
        freq = 220 + 20 * math.sin(i * 0.1)  # Gentle frequency variation
        segment = generator.generate_tone(freq, 0.1, 0.1)
        ambient_data.extend(segment)
    generator.save_wave_file(ambient_data, 'sounds/background_ambient.wav')
    
    print("\n✅ تم إنشاء جميع المؤثرات الصوتية بنجاح!")
    print("✅ All sound effects created successfully!")
    
    # List created files
    sound_files = [
        'button_click.wav - صوت النقر على الأزرار',
        'raffle_start.wav - صوت بداية السحب',
        'rolling.wav - صوت الدوران أثناء السحب',
        'winner.wav - صوت إعلان الفائز',
        'celebration.wav - صوت الاحتفال',
        'drum_roll.wav - صوت الطبلة',
        'success_chime.wav - نغمة النجاح',
        'error.wav - صوت الخطأ',
        'background_ambient.wav - موسيقى خلفية هادئة'
    ]
    
    print("\n📁 الملفات الصوتية المنشأة:")
    print("📁 Created sound files:")
    for file in sound_files:
        print(f"   🎵 {file}")

if __name__ == "__main__":
    try:
        create_sound_effects()
    except ImportError:
        print("❌ numpy is required for sound generation")
        print("Install with: pip install numpy")
    except Exception as e:
        print(f"❌ Error creating sound effects: {e}")
